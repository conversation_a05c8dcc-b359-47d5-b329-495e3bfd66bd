/* Chat Widget Styles */

/* Modern, interactive, scrollable tables in chat - Custom Palette */

.message-bot table, .message-user table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #DFE7EA; /* Corporate light blue */
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(1,30,65,0.04);
  margin: 1em 0;
  border: 1px solid #7B8A9C; /* Corporate mid blue */
}
.message-bot table th, .message-user table th,
.message-bot table td, .message-user table td {
  border-right: 1px solid #BFBAB0; /* Warm Gray */
}
.message-bot table th:last-child, .message-user table th:last-child,
.message-bot table td:last-child, .message-user table td:last-child {
  border-right: none;
}
.message-bot table thead, .message-user table thead {
  background: #BCD4D2; /* Deep Teal light */
}
.message-bot table th, .message-user table th {
  padding: 0.75em 1em;
  font-weight: 600;
  border-bottom: 2px solid #BFBAB0;
  background: #BCD4D2;
  color: #011E41;
  position: relative;
}
.message-bot table th:hover, .message-user table th:hover {
  background: #C4D69A; /* Sustainability light */
}
.message-bot table td, .message-user table td {
  padding: 0.75em 1em;
  border-bottom: 1px solid #D9D5D2; /* Warm Gray light */
}
.message-bot table tr:nth-child(even), .message-user table tr:nth-child(even) {
  background: #F5F7F8; /* very light blue/gray */
}
.message-bot table tr:hover, .message-user table tr:hover {
  background: #A8B580; /* Sustainability mid */
}
.message-bot .chat-table-scroll, .message-user .chat-table-scroll {
  overflow-x: auto;
  width: 100%;
  display: block;
  max-width: 100%;
  padding-bottom: 2px;
}
.chat-table-scroll::-webkit-scrollbar {
  height: 8px;
}
.chat-table-scroll::-webkit-scrollbar-thumb {
  background: #BFBAB0;
  border-radius: 4px;
}
.chat-table-scroll::-webkit-scrollbar-track {
  background: #DFE7EA;
  border-radius: 4px;
}
.message-bot table, .message-user table {
  min-width: 600px;
}
@media (max-width: 700px) {
  .message-bot table, .message-user table {
    min-width: 400px;
  }
}

@media (max-width: 500px) {
  .message-bot table, .message-user table {
    min-width: 300px;
  }
}

/* Chat Widget Main Styles */
.hidden { display: none; }

.chat-bubble-hidden {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
}

.chat-bubble-visible {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

#chat-widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  flex-direction: column;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 99999;
}

#chat-popup {
  height: 70vh;
  max-height: 70vh;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(1,30,65,0.18);
  font-family: inherit;
  opacity: 0;
  background: #FFFFFF;
  border: 2px solid #011E41;
  border-radius: 18px;
  transform: translateY(40px) scale(0.98);
  pointer-events: none;
}

#chat-popup.open {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

#chat-popup.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  z-index: 9999;
  box-shadow: none !important;
}

#chat-popup.fullscreen-animating {
  transition: all 0.4s cubic-bezier(.4,0,.2,1);
}

@media (max-width: 768px) {
  #chat-popup {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-height: 100%;
    border-radius: 0;
  }
}

/* Scrollbar styles */
#chat-messages::-webkit-scrollbar { width: 6px; }
#chat-messages::-webkit-scrollbar-track { background: rgba(0,0,0,0.05); border-radius: 3px; }
#chat-messages::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); border-radius: 3px; }
#chat-messages::-webkit-scrollbar-thumb:hover { background: rgba(0,0,0,0.3); }

/* Message animations */
@keyframes slideInLeft { from { opacity: 0; transform: translateX(-20px); } to { opacity: 1; transform: translateX(0); } }
@keyframes slideInRight { from { opacity: 0; transform: translateX(20px); } to { opacity: 1; transform: translateX(0); } }
.message-bot { animation: slideInLeft 0.4s ease-out; }
.message-user { animation: slideInRight 0.4s ease-out; }

/* Button styles */
.chat-btn {
  background: linear-gradient(90deg, #7D653F 0%, #9E8864 100%); /* Golden */
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5em 1.2em;
  font-weight: 600;
  font-size: 1em;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(1,30,65,0.08);
}
.chat-btn:hover {
  background: linear-gradient(90deg, #264E59 0%, #526D70 100%); /* Deep Teal */
  color: #fff;
  transform: translateY(-1px) scale(1.03);
  box-shadow: 0 4px 12px rgba(1,30,65,0.18);
}

/* Enhanced input focus */
#chat-input:focus {
  outline: 2px solid #7B8A9C;
  border-color: #7B8A9C;
  box-shadow: 0 0 0 3px #DFE7EA;
}

/* Loader animation */
@keyframes spin { 100% { transform: rotate(360deg); } }
@keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
.loader {
  border: 2px solid #BFBAB0;
  border-top: 2px solid #7D653F;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  animation: spin 1s linear infinite;
  display: inline-block;
}
.loader + span {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Chat bubble styles */
.message-bot, .message-user {
  background: transparent;
  color: #011E41;
  border-radius: 18px 18px 6px 18px;
}
.message-user {
  background: transparent;
  color: #011E41;
}
/* Add shadow to the actual user message bubble content */
.message-user .bg-gradient-to-r {
  box-shadow: 0 4px 12px rgba(1,30,65,0.15), 0 2px 4px rgba(1,30,65,0.08) !important;
}
.message-bot .prose, .message-user .prose {
  color: #011E41;
}

/* Selection prompt styles */
.chat-rerun-controls, .chat-gallery-wrapper, .chat-gallery-container {
  background: #F5F7F8;
}

/* Selection form button */
form button[type='submit'] {
  background: linear-gradient(90deg, #6E824F 0%, #A8B580 100%);
  color: #011E41;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(110,130,79,0.08);
}
form button[type='submit']:hover {
  background: linear-gradient(90deg, #7D653F 0%, #9E8864 100%);
  color: #fff;
}

/* Inputs in selection */
input[type='radio'], input[type='checkbox'] {
  accent-color: #264E59;
}

/* General text */
.chat-title, .chat-header, .chat-widget-title {
  color: #011E41;
}
.chat-subtitle, .chat-secondary, .chat-meta {
  color: #7B8A9C;
}
.chat-muted {
  color: #BFBAB0;
}

/* Gallery Styles */

/* Fullscreen mode: force chat bubble and gallery to use full width */
#chat-popup.fullscreen .message-bot,
#chat-popup.fullscreen .message-user {
  max-width: 100vw !important;
  width: 100% !important;
  border-radius: 0 !important;
  box-sizing: border-box !important;
}
#chat-popup.fullscreen .chat-gallery-container {
  max-width: 100vw !important;
  width: 100% !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)) !important;
  gap: 16px !important;
}
#chat-popup.fullscreen .chat-gallery-many {
  max-height: 80vh !important;
}
#chat-popup.fullscreen #chat-messages {
  max-width: 100vw !important;
  width: 100% !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Gallery Styles */
.chat-gallery-img {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  border: 2px solid transparent;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.chat-gallery-img:hover {
  transform: scale(1.02) rotate(0.5deg);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  z-index: 2;
  border-color: rgba(59, 130, 246, 0.3);
}
.chat-gallery-container {
  display: grid;
  margin: auto;
  gap: 8px;
  margin-top: 1rem;
  max-width: 100%;
  width: 100%;
}

/* Make the chat bubble wide enough for the gallery */
.message-bot .chat-gallery-container,
.message-user .chat-gallery-container {
  max-width: 600px;
  width: 100%;
}
.message-bot, .message-user {
  max-width: 650px !important;
  width: 100%;
}
.chat-gallery-1 { grid-template-columns: 1fr; max-width: 500px; }
.chat-gallery-1 .chat-gallery-img { max-height: 200px; aspect-ratio: 4/3; }
.chat-gallery-2 { grid-template-columns: 1fr 1fr; }
.chat-gallery-2 .chat-gallery-img { aspect-ratio: 1; max-height: 150px; }
.chat-gallery-3 { grid-template-columns: 2fr 1fr; grid-template-rows: 1fr 1fr; }
.chat-gallery-3 .chat-gallery-img:first-child { grid-row: 1 / 3; aspect-ratio: 3/4; max-height: 200px; }
.chat-gallery-3 .chat-gallery-img:not(:first-child) { aspect-ratio: 1; max-height: 95px; }
.chat-gallery-4 { grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; }
.chat-gallery-4 .chat-gallery-img { aspect-ratio: 1; max-height: 120px; }
.chat-gallery-many { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); max-height: 1000px; }
.chat-gallery-many .chat-gallery-img { aspect-ratio: 1; max-height: 100px; min-height: 80px; }

@media (max-width: 900px) {
  .message-bot, .message-user {
    max-width: 98vw !important;
  }
  .chat-gallery-container {
    max-width: 98vw;
  }
}
@media (max-width: 40%) {
  .chat-gallery-1 .chat-gallery-img { max-height: 180px; }
  .chat-gallery-2, .chat-gallery-3, .chat-gallery-4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
  }
  .chat-gallery-3 .chat-gallery-img:first-child { grid-row: 1; aspect-ratio: 1; max-height: 120px; }
  .chat-gallery-3 .chat-gallery-img:not(:first-child),
  .chat-gallery-4 .chat-gallery-img { max-height: 120px; }
  .chat-gallery-many { grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)); }
  .chat-gallery-many .chat-gallery-img { max-height: 80px; min-height: 60px; }
}

#chat-image-modal.hidden {
  opacity: 0;
  visibility: hidden;
}
#chat-image-modal:not(.hidden) {
  opacity: 1;
  visibility: visible;
}
#chat-image-modal-img {
  max-height: 90vh !important;
  max-width: 90vw !important;
  height: auto !important;
  width: auto;
  display: block;
  margin: 0 auto;
}
