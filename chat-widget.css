/* Chat Widget Styles */

/* Utility Classes (replacing Tai<PERSON><PERSON>) */
.hidden { display: none !important; }
.flex { display: flex; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.flex-shrink-0 { flex-shrink: 0; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-start { justify-content: flex-start; }
.space-x-3 > * + * { margin-left: 0.75rem; }

/* Positioning */
.absolute { position: absolute; }
.relative { position: relative; }
.fixed { position: fixed; }
.bottom-0 { bottom: 0; }
.right-0 { right: 0; }
.top-0 { top: 0; }
.left-0 { left: 0; }

/* Sizing */
.w-20 { width: 5rem; }
.h-20 { height: 5rem; }
.w-96 { width: 24rem; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-w-0 { min-width: 0; }
.max-w-none { max-width: none; }

/* Spacing */
.p-5 { padding: 1.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-3 { margin-right: 0.75rem; }

/* Colors */
.text-white { color: #ffffff; }
.text-gray-600 { color: #4b5563; }
.text-gray-800 { color: #1f2937; }
.text-red-800 { color: #991b1b; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Backgrounds */
.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.from-blue-700 { --tw-gradient-from: #1d4ed8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(29, 78, 216, 0)); }
.from-blue-900 { --tw-gradient-from: #1e3a8a; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(30, 58, 138, 0)); }
.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }
.from-gray-100 { --tw-gradient-from: #f3f4f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(243, 244, 246, 0)); }
.from-gray-50 { --tw-gradient-from: #f9fafb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0)); }
.from-red-50 { --tw-gradient-from: #fef2f2; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(254, 242, 242, 0)); }
.to-gray-300 { --tw-gradient-to: #d1d5db; }
.to-gray-400 { --tw-gradient-to: #9ca3af; }
.to-gray-200 { --tw-gradient-to: #e5e7eb; }
.to-indigo-900 { --tw-gradient-to: #312e81; }
.to-indigo-600 { --tw-gradient-to: #4f46e5; }
.to-indigo-700 { --tw-gradient-to: #4338ca; }
.to-white { --tw-gradient-to: #ffffff; }
.to-red-100 { --tw-gradient-to: #fee2e2; }

/* Background opacity variants */
.bg-white-90 { background-color: rgba(255, 255, 255, 0.9); }
.bg-black-75 { background-color: rgba(0, 0, 0, 0.75); }
.from-gray-50-50 { --tw-gradient-from: rgba(249, 250, 251, 0.5); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0)); }
.to-white-50 { --tw-gradient-to: rgba(255, 255, 255, 0.5); }

/* Borders */
.border { border-width: 1px; }
.border-4 { border-width: 4px; }
.border-t { border-top-width: 1px; }
.border-white { border-color: #ffffff; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-200 { border-color: #e5e7eb; }
.border-red-200 { border-color: #fecaca; }

/* Border Radius */
.rounded-full { border-radius: 9999px; }
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-bl-md { border-bottom-left-radius: 0.375rem; }
.rounded-br-md { border-bottom-right-radius: 0.375rem; }
.rounded-b-2xl { border-bottom-left-radius: 1rem; border-bottom-right-radius: 1rem; }

/* Shadows */
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.shadow-3xl { box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

/* Hover effects */
.hover-from-blue-700:hover { --tw-gradient-from: #1d4ed8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(29, 78, 216, 0)); }
.hover-to-gray-400:hover { --tw-gradient-to: #9ca3af; }
.hover-to-indigo-700:hover { --tw-gradient-to: #4338ca; }
.hover-shadow-3xl:hover { box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3); }
.hover-shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover-shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Typography */
.font-medium { font-weight: 500; }
.font-weight-600 { font-weight: 600; }
.break-all { word-break: break-all; }

/* Transitions */
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }
.duration-200 { transition-duration: 200ms; }

/* Cursor */
.cursor-pointer { cursor: pointer; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Backdrop */
.backdrop-blur-lg { -webkit-backdrop-filter: blur(16px); backdrop-filter: blur(16px); }
.backdrop-blur-sm { -webkit-backdrop-filter: blur(4px); backdrop-filter: blur(4px); }

/* Opacity */
.bg-white-95 { background-color: rgba(255, 255, 255, 0.95); }
.bg-white-80 { background-color: rgba(255, 255, 255, 0.8); }
.border-gray-200-50 { border-color: rgba(229, 231, 235, 0.5); }

/* Max width constraints */
.max-w-75 { max-width: 75%; }
.max-w-85 { max-width: 85%; }

/* Outline */
.outline-none { outline: 2px solid transparent; outline-offset: 2px; }

/* Focus states */
.focus-border-blue-500:focus { border-color: #3b82f6; }
.focus-ring-2:focus { box-shadow: 0 0 0 2px var(--tw-ring-color); }
.focus-ring-blue-500-20:focus { --tw-ring-color: rgba(59, 130, 246, 0.2); }

/* Z-index */
.z-50 { z-index: 50; }

/* Inset */
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* Max dimensions */
.max-w-90vw { max-width: 90vw; }
.max-h-90vh { max-height: 90vh; }
.max-h-85vh { max-height: 85vh; }
.max-w-85vw { max-width: 85vw; }

/* Placeholder */
.placeholder-gray-500::placeholder { color: #6b7280; }

/* Backdrop blur variants */
.backdrop-blur-md { -webkit-backdrop-filter: blur(12px); backdrop-filter: blur(12px); }

/* Group hover effects */
.group:hover .group-hover\\:scale-110 { transform: scale(1.1); }

/* Scrollbar */
.scrollbar-thin::-webkit-scrollbar { width: 6px; }
.scrollbar-thin::-webkit-scrollbar-track { background: rgba(0,0,0,0.05); border-radius: 3px; }
.scrollbar-thin::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); border-radius: 3px; }
.scrollbar-thin::-webkit-scrollbar-thumb:hover { background: rgba(0,0,0,0.3); }

/* Modern, interactive, scrollable tables in chat - Custom Palette */

.message-bot table, .message-user table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #DFE7EA; /* Corporate light blue */
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(1,30,65,0.04);
  margin: 1em 0;
  border: 1px solid #7B8A9C; /* Corporate mid blue */
}
.message-bot table th, .message-user table th,
.message-bot table td, .message-user table td {
  border-right: 1px solid #BFBAB0; /* Warm Gray */
}
.message-bot table th:last-child, .message-user table th:last-child,
.message-bot table td:last-child, .message-user table td:last-child {
  border-right: none;
}
.message-bot table thead, .message-user table thead {
  background: #BCD4D2; /* Deep Teal light */
}
.message-bot table th, .message-user table th {
  padding: 0.75em 1em;
  font-weight: 600;
  border-bottom: 2px solid #BFBAB0;
  background: #BCD4D2;
  color: #011E41;
  position: relative;
}
.message-bot table th:hover, .message-user table th:hover {
  background: #C4D69A; /* Sustainability light */
}
.message-bot table td, .message-user table td {
  padding: 0.75em 1em;
  border-bottom: 1px solid #D9D5D2; /* Warm Gray light */
}
.message-bot table tr:nth-child(even), .message-user table tr:nth-child(even) {
  background: #F5F7F8; /* very light blue/gray */
}
.message-bot table tr:hover, .message-user table tr:hover {
  background: #A8B580; /* Sustainability mid */
}
.message-bot .chat-table-scroll, .message-user .chat-table-scroll {
  overflow-x: auto;
  width: 100%;
  display: block;
  max-width: 100%;
  padding-bottom: 2px;
}
.chat-table-scroll::-webkit-scrollbar {
  height: 8px;
}
.chat-table-scroll::-webkit-scrollbar-thumb {
  background: #BFBAB0;
  border-radius: 4px;
}
.chat-table-scroll::-webkit-scrollbar-track {
  background: #DFE7EA;
  border-radius: 4px;
}
.message-bot table, .message-user table {
  min-width: 600px;
}
@media (max-width: 700px) {
  .message-bot table, .message-user table {
    min-width: 400px;
  }
}

@media (max-width: 500px) {
  .message-bot table, .message-user table {
    min-width: 300px;
  }
}

/* Chat Widget Main Styles */
.hidden { display: none; }

.chat-bubble-hidden {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
}

.chat-bubble-visible {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

#chat-widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  flex-direction: column;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 99999;
}

#chat-popup {
  height: 70vh;
  max-height: 70vh;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(1,30,65,0.18);
  font-family: inherit;
  opacity: 0;
  background: #FFFFFF;
  border: 2px solid #011E41;
  border-radius: 18px;
  transform: translateY(40px) scale(0.98);
  pointer-events: none;
}

#chat-popup.open {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

#chat-popup.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  z-index: 9999;
  box-shadow: none !important;
}

#chat-popup.fullscreen-animating {
  transition: all 0.4s cubic-bezier(.4,0,.2,1);
}

@media (max-width: 768px) {
  #chat-popup {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-height: 100%;
    border-radius: 0;
  }
}

/* Scrollbar styles */
#chat-messages::-webkit-scrollbar { width: 6px; }
#chat-messages::-webkit-scrollbar-track { background: rgba(0,0,0,0.05); border-radius: 3px; }
#chat-messages::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); border-radius: 3px; }
#chat-messages::-webkit-scrollbar-thumb:hover { background: rgba(0,0,0,0.3); }

/* Message animations */
@keyframes slideInLeft {
  from { opacity: 0; transform: translate3d(-20px, 0, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}
@keyframes slideInRight {
  from { opacity: 0; transform: translate3d(20px, 0, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}
.message-bot {
  animation: slideInLeft 0.4s ease-out;
  will-change: transform, opacity;
}
.message-user {
  animation: slideInRight 0.4s ease-out;
  will-change: transform, opacity;
}

/* Button styles */
.chat-btn {
  background: linear-gradient(90deg, #7D653F 0%, #9E8864 100%); /* Golden */
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5em 1.2em;
  font-weight: 600;
  font-size: 1em;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(1,30,65,0.08);
}
.chat-btn:hover {
  background: linear-gradient(90deg, #264E59 0%, #526D70 100%); /* Deep Teal */
  color: #fff;
  transform: translateY(-1px) scale(1.03);
  box-shadow: 0 4px 12px rgba(1,30,65,0.18);
}

/* Enhanced input focus */
#chat-input:focus {
  outline: 2px solid #7B8A9C;
  border-color: #7B8A9C;
  box-shadow: 0 0 0 3px #DFE7EA;
}

/* Loader animation */
@keyframes spin {
  100% { transform: rotate3d(0, 0, 1, 360deg); }
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
.loader {
  border: 2px solid #BFBAB0;
  border-top: 2px solid #7D653F;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  animation: spin 1s linear infinite;
  display: inline-block;
}
.loader + span {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Chat bubble styles */
.message-bot, .message-user {
  background: transparent;
  color: #011E41;
  border-radius: 18px 18px 6px 18px;
}
.message-user {
  background: transparent;
  color: #011E41;
}
/* Add shadow to the actual user message bubble content */
.message-user .bg-gradient-to-r {
  box-shadow: 0 4px 12px rgba(1,30,65,0.15), 0 2px 4px rgba(1,30,65,0.08) !important;
}
.message-bot .prose, .message-user .prose {
  color: #011E41;
}

/* Selection prompt styles */
.chat-rerun-controls, .chat-gallery-wrapper, .chat-gallery-container {
  background: #F5F7F8;
}

/* Selection form button */
form button[type='submit'] {
  background: linear-gradient(90deg, #6E824F 0%, #A8B580 100%);
  color: #011E41;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(110,130,79,0.08);
}
form button[type='submit']:hover {
  background: linear-gradient(90deg, #7D653F 0%, #9E8864 100%);
  color: #fff;
}

/* Inputs in selection */
input[type='radio'], input[type='checkbox'] {
  accent-color: #264E59;
}

/* General text */
.chat-title, .chat-header, .chat-widget-title {
  color: #011E41;
}
.chat-subtitle, .chat-secondary, .chat-meta {
  color: #7B8A9C;
}
.chat-muted {
  color: #BFBAB0;
}

/* Gallery Styles */

/* Fullscreen mode: force chat bubble and gallery to use full width */
#chat-popup.fullscreen .message-bot,
#chat-popup.fullscreen .message-user {
  max-width: 100vw !important;
  width: 100% !important;
  border-radius: 0 !important;
  box-sizing: border-box !important;
}
#chat-popup.fullscreen .chat-gallery-container {
  max-width: 100vw !important;
  width: 100% !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)) !important;
  gap: 16px !important;
}
#chat-popup.fullscreen .chat-gallery-many {
  max-height: 80vh !important;
}
#chat-popup.fullscreen #chat-messages {
  max-width: 100vw !important;
  width: 100% !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Gallery Styles */
.chat-gallery-img {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  border: 2px solid transparent;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.chat-gallery-img:hover {
  transform: scale(1.02) rotate(0.5deg);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  z-index: 2;
  border-color: rgba(59, 130, 246, 0.3);
}
.chat-gallery-container {
  display: grid;
  margin: auto;
  gap: 8px;
  margin-top: 1rem;
  max-width: 100%;
  width: 100%;
}

/* Make the chat bubble wide enough for the gallery */
.message-bot .chat-gallery-container,
.message-user .chat-gallery-container {
  max-width: 600px;
  width: 100%;
}
.message-bot, .message-user {
  max-width: 650px !important;
  width: 100%;
}
.chat-gallery-1 { grid-template-columns: 1fr; max-width: 500px; }
.chat-gallery-1 .chat-gallery-img { max-height: 200px; aspect-ratio: 4/3; }
.chat-gallery-2 { grid-template-columns: 1fr 1fr; }
.chat-gallery-2 .chat-gallery-img { aspect-ratio: 1; max-height: 150px; }
.chat-gallery-3 { grid-template-columns: 2fr 1fr; grid-template-rows: 1fr 1fr; }
.chat-gallery-3 .chat-gallery-img:first-child { grid-row: 1 / 3; aspect-ratio: 3/4; max-height: 200px; }
.chat-gallery-3 .chat-gallery-img:not(:first-child) { aspect-ratio: 1; max-height: 95px; }
.chat-gallery-4 { grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; }
.chat-gallery-4 .chat-gallery-img { aspect-ratio: 1; max-height: 120px; }
.chat-gallery-many { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); max-height: 1000px; }
.chat-gallery-many .chat-gallery-img { aspect-ratio: 1; max-height: 100px; min-height: 80px; }

@media (max-width: 900px) {
  .message-bot, .message-user {
    max-width: 98vw !important;
  }
  .chat-gallery-container {
    max-width: 98vw;
  }
}
@media (max-width: 40%) {
  .chat-gallery-1 .chat-gallery-img { max-height: 180px; }
  .chat-gallery-2, .chat-gallery-3, .chat-gallery-4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
  }
  .chat-gallery-3 .chat-gallery-img:first-child { grid-row: 1; aspect-ratio: 1; max-height: 120px; }
  .chat-gallery-3 .chat-gallery-img:not(:first-child),
  .chat-gallery-4 .chat-gallery-img { max-height: 120px; }
  .chat-gallery-many { grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)); }
  .chat-gallery-many .chat-gallery-img { max-height: 80px; min-height: 60px; }
}

#chat-image-modal.hidden {
  opacity: 0;
  visibility: hidden;
}
#chat-image-modal:not(.hidden) {
  opacity: 1;
  visibility: visible;
}
#chat-image-modal-img {
  max-height: 90vh !important;
  max-width: 90vw !important;
  height: auto !important;
  width: auto;
  display: block;
  margin: 0 auto;
}
