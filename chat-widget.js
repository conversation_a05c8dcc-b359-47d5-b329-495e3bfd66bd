/**
 * Chat Widget
**/

(function() {
  'use strict';

  // Configuration
  const CONFIG = {
    bot: 'CALL_CENTER_BOT',
    apiUrl: 'http://localhost:3001/',
    answerType: 'html',
  };

  // State management
  const state = {
    conversationId: window._chatWidgetConversationId,
    dialogId: window._chatWidgetDialogId,
    isOpen: false,
    isFullscreen: false
  };

  // Utility functions
  const utils = {
    generateId: () => Math.random().toString(36).substring(2, 12),
    
    escapeHtml: (text) => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    },

    debounce: (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    downloadJson: (data, filename) => {
      try {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        
        a.href = url;
        a.download = filename || `chat-response-${new Date().toISOString().slice(0, 19).replace('T', '_').replace(/:/g, '-')}.json`;
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        return true;
      } catch (error) {
        console.error('Error downloading JSON:', error);
        return false;
      }
    }
  };

  // CSS injection module
  const cssInjector = {
    injectExternalStyles: () => {
      const styles = [
        'https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.16/tailwind.min.css',
        'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap',
        'https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap'
      ];

      styles.forEach(href => {
        if (!document.querySelector(`link[href="${href}"]`)) {
          const link = document.createElement('link');
          link.href = href;
          link.rel = 'stylesheet';
          document.head.appendChild(link);
        }
      });
    },

    injectChatWidgetStyles: () => {
      if (document.getElementById('chat-widget-styles')) return;

      const link = document.createElement('link');
      link.id = 'chat-widget-styles';
      link.href = './chat-widget.css';
      link.rel = 'stylesheet';
      document.head.appendChild(link);
    },

    // Legacy methods - kept for compatibility but now do nothing
    injectMainStyles: () => {
      // This method is now handled by injectChatWidgetStyles
      return;
    },

    injectGalleryStyles: () => {
      // This method is now handled by injectChatWidgetStyles
      return;
    }
  };

  // HTML templates
  // Inject SheetJS for XLSX export
  if (!window.XLSX) {
    const xlsxScript = document.createElement('script');
    xlsxScript.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
    document.head.appendChild(xlsxScript);
  }

  // ...existing code...
  const templates = {
    chatWidget: () => `
      <div id="chat-bubble" class="chat-bubble-hidden group w-20 h-20 bg-gradient-to-br from-blue-700 to-gray-300 hover:from-blue-700 hover:to-gray-400 rounded-full flex items-center justify-center cursor-pointer text-3xl shadow-2xl border-4 border-white hover:shadow-3xl transition-all duration-300 overflow-hidden relative">
        ${templates.robotIcon()}
      </div>
      <div id="chat-popup" class="hidden absolute bottom-0 right-0 w-96 bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-gray-200/50 flex flex-col transition-all text-sm">
        ${templates.chatHeader()}
        ${templates.chatMessages()}
        ${templates.chatInput()}
      </div>
    `,

    robotIcon: () => `
      <svg
        style="transform: scaleX(-1);"
        width="100%"
        height="100%"
        viewBox="0 0 793.70081 1122.5197"
        version="1.1"
        id="svg1"
        xml:space="preserve"
        inkscape:version="1.4 (86a8ad7, 2024-10-11)"
        sodipodi:docname="eprobot_white.svg"
        inkscape:export-filename="eprobot_white.png"
        inkscape:export-xdpi="68.419998"
        inkscape:export-ydpi="68.419998"
        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:svg="http://www.w3.org/2000/svg"><sodipodi:namedview
          id="namedview1"
          pagecolor="#ffffff"
          bordercolor="#000000"
          borderopacity="0.25"
          inkscape:showpageshadow="2"
          inkscape:pageopacity="0.0"
          inkscape:pagecheckerboard="0"
          inkscape:deskcolor="#d1d1d1"
          inkscape:document-units="mm"
          inkscape:zoom="0.72000791"
          inkscape:cx="241.66401"
          inkscape:cy="338.19073"
          inkscape:window-width="2560"
          inkscape:window-height="1361"
          inkscape:window-x="1911"
          inkscape:window-y="-9"
          inkscape:window-maximized="1"
          inkscape:current-layer="g25"
          inkscape:export-bgcolor="#ffffffff" /><defs
          id="defs1" /><g
          inkscape:groupmode="layer"
          id="layer2"
          inkscape:label="Livello 2"><g
            id="g25"
            transform="matrix(-0.95668354,0,0,0.95668354,762.0401,45.349554)"><path
              style="display:inline;fill:#ffffff;fill-opacity:1"
              d="m 232.27983,1091.0715 c -12.31416,-0.6597 -20.81346,-9.4154 -20.77986,-19.9322 0.05,-15.6506 10.06645,-31.3764 26.73739,-41.9776 3.00469,-1.9107 5.44695,-4.2612 5.69823,-5.4842 2.94808,-14.3483 3.07808,-19.6877 0.49579,-20.363 -2.72645,-0.7129 -15.03354,-6.83078 -17.04883,-8.47494 -2.9303,-2.39064 -4.90365,-7.62185 -4.90495,-13.00267 -0.003,-12.88932 7.07879,-57.23592 13.43125,-84.10604 2.0022,-8.46907 2.35994,-11.67351 1.65398,-14.81551 -0.56099,-2.49675 -0.51051,-6.63872 0.13381,-10.97984 0.9319,-6.27877 -0.27918,-24.29302 -1.73079,-25.74463 -0.2257,-0.2257 -2.5651,0.18765 -5.19867,0.91855 -3.20103,0.88839 -11.41809,1.35115 -24.7883,1.39601 -14.83015,0.0498 -20.62129,0.41599 -22.40352,1.41679 -4.09957,2.30211 -16.99709,0.89335 -29.09648,-3.17812 -25.12373,-8.45418 -35.24146,-14.18742 -41.97322,-23.78419 -5.31837,-7.58186 -6.30182,-8.17027 -19.026776,-11.38378 -38.677807,-9.76756 -63.789305,-23.58525 -73.228081,-40.29402 -2.025028,-3.58476 -4.83889,-6.56704 -8.852686,-9.38256 -16.3424975,-11.46361 -19.1154375,-26.83675 -7.4607525,-41.36232 1.765184,-2.2 4.478928,-6.18361 6.030541,-8.85247 C 18.404119,697.17404 31.679311,692.3199 60.297003,693.28168 l 15.369341,0.51654 -0.64683,-2.6305 c -0.35576,-1.44677 -0.90494,-6.35383 -1.2204,-10.90457 -1.11439,-16.07586 5.97018,-26.53176 19.37,-28.58758 10.775636,-1.65321 20.622726,3.97669 28.293566,16.17633 7.42441,11.80772 8.38207,12.72314 19.23836,18.38971 10.91815,5.69886 19.75916,12.92209 21.77608,17.79138 1.3008,3.14041 2.06214,3.46781 10.50176,4.5161 8.27004,1.02723 25.46908,6.33579 34.2666,10.57654 l 8.26661,3.98483 4.23339,-2.87802 c 2.32837,-1.58291 7.6084,-4.81524 11.7334,-7.18294 10.96393,-6.29317 41.18545,-26.40663 43.41053,-28.8912 1.0508,-1.17333 1.69157,-2.35231 1.42393,-2.61994 -0.26763,-0.26764 -8.3839,-2.5027 -18.03614,-4.96682 -70.10184,-17.89625 -111.22637,-45.67543 -137.30861,-92.75049 -4.30247,-7.76539 -5.57241,-9.21033 -11.64198,-13.24633 C 79.933324,551.0295 67.75615,488.86987 83.605454,439.2784 c 5.81789,-18.20382 14.20707,-31.43092 25.280936,-39.86009 6.33721,-4.82372 6.46767,-5.0221 11.10213,-16.88108 16.84081,-43.09351 40.40664,-75.63303 71.91119,-99.29442 3.87715,-2.91193 7.34507,-5.83693 7.70648,-6.5 0.89741,-1.64648 22.34562,-65.98623 22.36067,-67.07693 0.007,-0.47925 -3.7331,-2.18983 -8.31046,-3.8013 C 144.11651,181.38295 144.90434,84.657093 200.92915,40.0768 c 0,0 18.74427,-18.60677 37.69938,-24.277869 31.53927,-9.4360971 78.01056,0.440221 92.57322,11.322602 -83.84251,246.733137 -193.82889,299.845427 2.92448,0.400278 11.04925,-9.305171 35.63191,-28.29556993 75.3461,-44.37158 39.71419,-16.07601 55.22337,-6.64 82.2682,5.677743 27.04482,12.3177436 57.98835,50.23547 57.98835,50.23547 l 3.75,4.057174 7.5,8.114348 6.14434,-1.598863 c 7.94595,-2.067673 29.64278,-2.149107 39.04028,-0.14653 39.3023,8.375195 69.92964,35.123232 86.82732,75.829637 2.60076,6.26522 3.35521,7.14038 9.90166,11.48594 38.66788,25.66787 52.74066,78.05771 32.16203,119.73209 -8.15565,16.51626 -22.48259,30.78124 -39.07563,38.90667 -12.45616,6.09963 -21.04326,7.96475 -34.01421,7.38787 -9.85271,-0.4382 -10.37707,-0.35982 -10.85452,1.62251 -0.87908,3.64987 -1.93642,46.14284 -1.24046,49.8526 0.36784,1.96078 2.96221,7.0782 5.76525,11.37203 5.50946,8.43965 11.52485,20.01565 14.35587,27.62644 1.50076,4.03456 2.82423,5.46656 8.60253,9.30797 15.93993,10.59684 26.16671,28.4647 31.49317,55.0237 2.47299,12.3309 2.49577,43.05576 0.0417,56.22796 -7.9783,42.8233 -32.80835,76.49675 -62.47323,84.72351 -5.82183,1.61452 -6.85396,2.35301 -13,9.3015 -17.9777,20.32493 -27.28957,28.89564 -42.43109,39.0539 -10.43792,7.00267 -33.56927,18.08054 -48.1444,23.05691 -17.37838,5.93348 -18.27468,7.88889 -6.32918,13.80812 8.13123,4.02918 14.59782,9.03988 33.72858,26.13488 38.45796,34.36554 52.07995,50.44344 65.08259,76.81627 5.23161,10.61113 5.92099,11.52587 10.96533,14.55007 12.14916,7.28371 22.13501,29.0474 23.67707,51.60307 0.49087,7.17986 1.05626,9.17911 3.95927,14 7.01645,11.65187 11.77134,32.26224 11.11629,48.18427 -0.75605,18.37731 -7.28136,28.64982 -23.21165,36.54107 -4.9989,2.47626 -12.14295,6.34345 -15.87568,8.59375 -5.88159,3.54575 -7.88221,4.19427 -15,4.86233 -5.61437,0.52696 -10.11176,1.65137 -14.21322,3.55352 -16.46185,7.63455 -32.82344,1.19523 -38.56804,-15.17897 l -2.43196,-6.93195 -5.5247,-0.59424 c -6.73248,-0.72415 -9.20331,-1.93497 -14.84057,-7.27256 l -4.36526,-4.1332 0.52773,5 c 0.29026,2.75 1.94713,11.525 3.68196,19.5 1.73482,7.975 4.46025,21.925 6.05651,31 1.64596,9.35758 4.25788,20.17936 6.0339,24.99996 2.9716,8.0657 3.10765,9.0621 2.66267,19.5 -0.42938,10.072 -0.25748,11.5062 2.03752,17 6.90227,16.5228 7.00836,29.244 0.30375,36.4213 0,0 -11.78456,9.6679 -19.08028,12.8995 -7.29572,3.2316 -9.98336,4.9 -27.34382,7.304 -17.36047,2.404 -78.40822,-17.1468 -81.03453,-19.2131 -4.48981,-3.5317 -5.89085,-7.5732 -6.56829,-18.9473 -0.60192,-10.106 -0.77237,-10.7252 -4.18129,-15.1898 -3.3842,-4.4321 -3.6314,-5.3095 -5.17992,-18.3848 -0.89437,-7.5518 -2.00846,-14.1129 -2.47576,-14.5802 -0.4673,-0.4673 -10.71813,-0.7281 -22.77963,-0.5796 l -21.92999,0.27 -0.67008,10.0908 c -0.38735,5.8333 -1.38344,11.7402 -2.36083,14 -1.24665,2.8824 -1.72732,7.1058 -1.82993,16.0789 -0.13157,11.5061 -0.27549,12.3157 -2.63916,14.845 -6.21561,6.6515 -23.2066,11.0494 -46.08742,11.9293 -6.27692,0.2414 -1.15345,-0.375 -14.90598,1.4974 -12.62334,1.7186 -41.01738,3.2994 -54.70736,2.566 z"
              id="path1"
              sodipodi:nodetypes="sssssssssssssssssssssscsssssssscssssssssssscssszzzzcccsssssssssssssssssssssssssssssscsscssssssszzcssssscssssssss"sscssssssss"
              transform="translate(2.1677938e-6)" /><path
              style="display:inline;fill:#b5bcc5"
              d="m 197.97888,1090.5556 c -0.55,-0.178 -4.29785,-1.3611 -8.32856,-2.6291 -6.97289,-2.1937 -12.17144,-5.5491 -12.17144,-7.8562 0,-1.9784 6.23309,-4.8673 14.88567,-6.8994 13.11453,-3.0799 16.09812,-3.4349 16.21401,-1.929 0.0548,0.7124 0.45997,0.1703 0.90032,-1.2047 0.71207,-2.2235 0.81166,-2.0816 0.90032,1.2821 0.0951,3.6074 1.40643,7.2238 4.61169,12.7179 0.80218,1.375 1.46514,2.1521 1.47325,1.727 0.008,-0.4252 1.16923,-0.017 2.58028,0.9079 1.41105,0.9246 4.89855,2.2467 7.75,2.938 4.81963,1.1686 4.02336,1.2574 -11.31554,1.2631 -9.075,0 -16.95,-0.1395 -17.5,-0.3176 z m 115.84109,-0.1928 c 0.37094,-0.371 6.10757,-0.5796 12.74808,-0.4638 6.96306,0.1215 11.83806,-0.1705 11.51708,-0.6899 -0.30612,-0.4953 2.75414,-1.2005 6.80058,-1.5672 14.09775,-1.2775 28.3285,-6.5333 32.04099,-11.8337 0.84701,-1.2092 1.82573,-4.3524 2.17493,-6.9848 0.3492,-2.6324 1.02694,-4.7862 1.50608,-4.7862 0.47914,0 0.87117,-0.6546 0.87117,-1.4547 0,-2.2169 4.31152,-2.5419 30.28686,-2.2827 19.07951,0.1904 23.95742,0.5135 24.64872,1.6326 0.68722,1.1124 0.94305,1.0111 1.26251,-0.5 0.22035,-1.0424 0.27887,2.1548 0.13005,7.1048 -0.18206,6.0556 0.0145,8.1821 0.60095,6.5 0.79279,-2.2741 0.88056,-2.1582 0.97123,1.2821 0.13495,5.1203 2.28822,9.9345 5.61336,12.55 l 2.75604,2.1679 h -67.30153 c -37.01584,0 -66.99803,-0.3035 -66.6271,-0.6744 z m 259.58415,-1.4706 c 3.24398,-3.0222 6.02694,-9.6392 6.0528,-14.3916 l 0.022,-4.0366 7.75,0.686 c 14.58841,1.2911 28.69593,5.5661 29.7949,9.0287 1.89874,5.9824 -13.27746,10.8585 -33.79552,10.8585 h -12.12651 z m -92.67592,-86.4951 c 1.89072,-0.5418 2.07945,-1.1463 3.81572,-12.22099 2.31209,-14.74771 0.89745,-52.96829 -2.27199,-61.38388 -0.58535,-1.55425 -1.79474,-2.75 -2.78135,-2.75 -0.96011,0 -9.70967,6.4125 -19.44346,14.25 -25.89112,20.84712 -26.81312,21.53612 -29.81824,22.28318 -3.93672,0.97864 -3.50407,-0.79249 1.54528,-6.32603 12.5844,-13.7911 34.26401,-45.9812 32.29431,-47.9509 -0.25042,-0.25042 -5.42253,0.70946 -11.49358,2.13305 -17.06319,4.00115 -39.32792,7.60767 -53.09601,8.60068 -13.6359,0.98348 -42,0.20991 -42,-1.14545 0,-0.46449 2.64171,-0.84453 5.87047,-0.84453 13.53078,0 61.54786,-10.451 87.94269,-19.14088 5.12224,-1.68637 9.14187,-3.23742 8.93252,-3.44677 -0.20935,-0.20935 -15.48416,0.0643 -33.94404,0.60817 -50.68669,1.49328 -92.96452,-1.01319 -136.99556,-8.12186 -7.31835,-1.18152 -16.26461,-2.36339 -19.88058,-2.62637 -6.31994,-0.45964 -6.67128,-0.36313 -9.07416,2.49253 l -2.49966,2.97068 -3.13752,-2.61775 c -3.93063,-3.27948 -5.50634,-7.4289 -4.67951,-12.32289 0.7581,-4.48713 0.34056,-4.39136 6.24859,-1.43308 13.10091,6.55991 45.46861,11.80522 90.71676,14.701 17.54767,1.12302 91.38636,0.94277 87.29941,-0.2131 -1.41935,-0.40142 -0.003,-0.69195 3.70059,-0.75908 23.16618,-0.4199 65.87391,-9.07595 86.69878,-17.57219 1.32248,-0.53956 1.89527,0.0462 2.58587,2.64425 2.8615,10.76523 -5.62004,16.72032 -43.24921,30.36627 -4.67846,1.69661 -4.83053,1.86017 -2.63883,2.83814 2.22839,0.99433 3.66764,0.78882 21.96623,-3.1365 2.30618,-0.49472 2.34899,-0.37768 1.78539,4.88128 -0.3176,2.96348 -0.23862,4.27601 0.1755,2.91673 0.82617,-2.71177 3.69095,29.63084 3.64438,41.14412 -0.0148,3.66999 0.32376,6.45593 0.75245,6.19098 0.85969,-0.53132 1.26846,6.84533 1.06042,19.13632 -0.10147,5.9947 0.064,7.12268 0.66016,4.5 l 0.79558,-3.5 0.12606,4.5 c 0.17602,6.28335 -1.54818,11.74776 -4.26943,13.53078 -2.38704,1.56401 -18.06375,5.79339 -25.09335,6.76979 -2.2,0.3056 -3.21281,0.33 -2.25068,0.054 z m 118.87269,-44.72909 c -5.12217,-1.85306 -8.12201,-5.92138 -8.12201,-11.01488 0,-5.67589 0.25736,-5.79229 6.30152,-2.85005 6.15455,2.99597 24.69848,4.6524 24.69848,2.20618 0,-0.7152 0.27925,-1.02112 0.62055,-0.67982 1.29408,1.29408 -5.87891,8.66263 -10.83868,11.13419 -5.69469,2.83779 -7.63469,3.02234 -12.65986,1.20438 z m 31.94219,-5.3084 c 0.22558,-2.87184 2.78394,-12.40571 2.85199,-10.62808 0.0811,2.11803 0.22215,2.14522 4.33381,0.83507 2.3375,-0.74483 5.95518,-1.59209 8.03928,-1.88281 l 3.78929,-0.52857 -3.06098,3.8648 c -3.51236,4.43471 -16.16625,11.0495 -15.95339,8.33959 z m -343.1996,-14.07238 c -12.88946,-13.21894 -24.46082,-28.07947 -27.64259,-35.5 -1.92043,-4.47885 2.28442,-3.59438 7.13045,1.49985 3.38301,3.55628 20.22347,25.71395 28.50118,37.50015 4.26157,6.06782 -0.83822,3.83361 -7.98904,-3.5 z m 283.05274,-4.07029 c -2.74528,-1.17903 -10.29344,-8.13695 -9.59016,-8.84023 0.37005,-0.37005 0.67282,-0.19049 0.67282,0.39902 0,0.69782 2.18097,0.90928 6.25,0.60598 9.39168,-0.70003 10.36811,-1.0547 12.63074,-4.58783 l 2.08556,-3.25665 0.0113,3 c 0.035,9.31627 -5.74418,15.3923 -12.06023,12.67971 z m -14.47085,-58.6808 c -3.97343,-6.78016 -6.37015,-13.80404 -6.90334,-20.23116 -0.26317,-3.17226 -0.23504,-4.98025 0.0625,-4.01775 0.29755,0.9625 0.95234,1.75 1.4551,1.75 0.50276,0 2.545,3.23852 4.53831,7.19671 1.99331,3.95819 4.64478,8.8134 5.89214,10.78935 l 2.26793,3.59264 -2.33506,2.71467 -2.33505,2.71466 z m -196.44649,2.50454 c -23.99874,-1.00369 -35.43835,-2.09156 -33.81165,-3.21538 0.82576,-0.57048 -0.17012,-0.68284 -2.68835,-0.30331 -7.95189,1.19845 -35.19311,-4.33913 -38.19181,-7.76361 -1.12282,-1.28225 -1.25567,-1.73925 -0.35049,-1.20567 0.80173,0.4726 2.37673,0.96189 3.5,1.0873 1.12326,0.12541 6.0923,0.92592 11.0423,1.77891 39.28741,6.77009 103.24797,8.24712 134.76653,3.11214 51.21052,-8.34319 60.61247,-20.42797 56.31331,-72.38225 -1.84425,-22.28744 -1.56072,-25.11158 2.52108,-25.11158 3.79162,0 7.63217,2.74675 25.56344,18.28294 5.5904,4.84369 10.89766,9.92587 11.79393,11.29374 0.89626,1.36788 1.97936,2.27087 2.40687,2.00665 0.64496,-0.39861 -1.74333,11.05825 -2.56714,12.31484 -0.57571,0.87816 -10.42727,-7.61168 -21.40278,-18.4444 -6.38262,-6.29957 -12.34512,-11.45377 -13.25,-11.45377 -5.26717,0 1.83946,13.07448 14.2937,26.29696 12.21386,12.96727 14.3529,17.4188 14.87346,30.95304 0.26583,6.9114 0.0394,9.75 -0.77771,9.75 -0.634,0 -0.87236,0.45364 -0.52969,1.00809 1.23596,1.99982 -16.57082,8.33906 -35.005,12.46183 -35.99332,8.04983 -82.13081,11.47281 -128.5,9.53353 z m -88.89107,-11.58487 c -6.79147,-1.8264 -13.44753,-4.75316 -16.79128,-7.38336 -3.84345,-3.02325 -0.79342,-2.66623 10.37781,1.21478 7.60696,2.64274 9.51419,2.99569 10.20736,1.88897 0.58821,-0.93913 0.98312,-0.24169 1.27401,2.25 0.23186,1.98607 0.39943,3.58141 0.37237,3.54521 -0.0271,-0.0362 -2.47518,-0.71821 -5.44027,-1.5156 z m -99.60893,-28.9169 c -0.55,-0.18723 -5.3438,-1.70298 -10.65288,-3.36832 -12.27708,-3.85106 -28.96673,-10.05698 -30.61105,-11.3825 -0.92819,-0.74823 0.40607,-1.58093 5.02309,-3.13487 8.3574,-2.81281 15.60493,-7.59271 21.81483,-14.38733 l 5.09851,-5.57858 7.66375,2.33902 c 11.52908,3.51874 23.98037,5.97528 30.38181,5.99409 5.21746,0.0153 5.84868,-0.22395 7.20996,-2.73319 0.82055,-1.5125 1.50763,-2.3 1.52685,-1.75 0.0756,2.1616 -9.73947,17.80873 -14.66964,23.38637 -7.2349,8.18504 -16.81429,12.64794 -22.78523,10.61531 z m 32.5,-2.92557 c 0,-0.39475 2.03447,-3.56878 4.52104,-7.0534 4.56174,-6.3927 12.47896,-22.09739 12.47896,-24.7534 0,-0.78979 0.58925,-1.43598 1.30944,-1.43598 5.31231,0 23.13034,-16.96461 22.34207,-21.27196 -0.24546,-1.34124 -0.0761,-5.05731 0.37627,-8.25793 0.73899,-5.2281 0.56453,-6.28097 -1.71721,-10.36345 -1.39687,-2.49927 -2.33361,-4.75029 -2.08164,-5.00226 0.25197,-0.25197 1.27188,1.34646 2.26647,3.55207 2.12246,4.70677 3.23682,5.05155 4.07302,1.2602 1.00121,-4.53956 1.26977,-4.89635 1.35263,-1.79706 l 0.079,2.95295 3.75,-3.52454 c 2.0625,-1.9385 4.95909,-4.27159 6.43687,-5.18464 l 2.68688,-1.6601 -2.54477,5.73169 c -7.53346,16.96798 -16.92918,46.9771 -19.88907,63.524 -0.90438,5.05582 -5.32761,9.29076 -11.74422,11.2443 -5.52659,1.68256 -23.69569,3.2464 -23.69569,2.03951 z m 335.22104,-6.00343 c -1.77157,-2.96187 -3.21236,-6.2149 -3.20175,-7.22896 0.0237,-2.26936 2.76694,-12.32869 2.89129,-10.60238 0.0492,0.68274 1.55192,3.1692 3.33942,5.52545 1.7875,2.35626 3.99472,5.39765 4.90494,6.75866 l 1.65494,2.47455 4.3271,-4 c 2.3799,-2.2 1.20989,-0.625 -2.60002,3.5 -3.80991,4.125 -7.18986,7.82803 -7.51099,8.22895 -0.32114,0.40093 -2.03335,-1.69439 -3.80493,-4.65627 z m -141.22243,-9.71139 c 7.6e-4,-0.35129 -1.47952,-0.91653 -3.28953,-1.25609 -10.93527,-2.05147 -25.3583,-12.61718 -33.22161,-24.33677 -4.45223,-6.63565 -7.499,-10.26843 -8.61201,-10.26843 -0.42675,0 -3.92807,3.77394 -7.7807,8.38654 -9.74304,11.66494 -18.55646,16.67011 -34.57403,19.63469 -4.39601,0.81363 -6.31312,1.52542 -5.1795,1.92307 0.98987,0.34723 -2.61946,0.61431 -8.02074,0.59351 l -9.82049,-0.0378 -0.31642,-2.75 c -0.31626,-2.74858 -0.94195,-4.3993 0.29347,-4.21507 l 2.02295,-0.20894 12.866,-0.42341 c 18.83547,-0.61987 34.96933,-12.61921 44.634,-27.74329 4.44476,-6.95553 6,-8.45728 6,-5.79365 0,2.81297 7.08945,15.32468 11.57178,20.42229 7.41936,8.4378 18.93275,15.59067 31.92822,19.8359 3.85,1.25767 6.325,2.33845 5.5,2.40171 -2.97319,0.228 2.46483,1.8345 6,1.77253 l 3.5,-0.0614 -3,-0.75279 c -2.36558,-0.5936 -2.07173,-0.6715 1.38957,-0.36836 2.41426,0.21144 5.03391,0.38444 5.82143,0.38444 1.19868,0 1.18228,0.24957 -0.10068,1.53253 -1.53636,1.53636 -17.6148,2.74944 -17.61171,1.32876 z m 30.2874,-15.36129 c 0.008,-2.75 0.20171,-3.75632 0.43157,-2.23625 0.22985,1.52006 0.22371,3.77006 -0.0136,5 -0.23736,1.22993 -0.42543,-0.0137 -0.41792,-2.76375 z m -295.86667,0.65597 c 0.86936,-1.68116 1.58066,-3.26422 1.58066,-3.51791 0,-0.2537 -6.6375,-0.41064 -14.75,-0.34878 -17.02212,0.12981 -23.643976,-1.17371 -44.896446,-8.83794 -19.852574,-7.15938 -38.353552,-17.56432 -38.353552,-21.57 0,-1.44638 1.23034,-1.09852 14.14024,3.99791 27.793182,10.97191 49.133172,16.45855 64.380788,16.5527 10.54609,0.0651 11.31425,-0.0763 17.82559,-3.28183 7.88828,-3.88339 13.49013,-10.52827 19.51628,-23.15012 3.12248,-6.54007 3.65724,-8.77991 4.17817,-17.5 l 0.77816,-0.75 c 0.16656,8.52307 0.33794,9.25 2.18077,9.25 1.7429,0 2,0.66666 2,5.18611 0,17.57186 -10.71758,36.4439 -25.16124,44.30512 l -5.00007,2.72139 z m 296.95472,-18.65597 c 0.002,-6.05 0.16697,-8.39607 0.36602,-5.21349 0.19905,3.18258 0.1972,8.13258 -0.004,11 -0.2013,2.86742 -0.36416,0.26349 -0.36191,-5.78651 z m -7.41531,-35.58806 c -1.30921,-2.79523 -4.76052,-7.55938 -7.66957,-10.58699 -2.90905,-3.02761 -5.28918,-5.6893 -5.28918,-5.91485 0,-0.22556 1.31365,-0.4101 2.91922,-0.4101 5.07322,0 27.55455,-4.85375 35.21806,-7.60362 10.69466,-3.83754 22.52972,-8.8037 34.68822,-14.55568 5.87097,-2.77746 12.15639,-5.692 13.9676,-6.47675 l 3.2931,-1.42681 -4.03926,7.35975 c -4.10289,7.47566 -6.90612,15.17646 -7.1723,19.70311 -0.14594,2.4818 -0.15025,2.48291 -0.59309,0.15297 l -0.44608,-2.34703 -2.22144,2.08692 c -1.22178,1.14781 -7.37226,3.93696 -13.66773,6.1981 -18.9804,6.8172 -24.32517,8.16828 -39.22651,9.91589 -9.72774,1.14086 -10.09269,1.37136 -8.14239,5.14283 0.86354,1.6699 1.3882,3.21805 1.16591,3.44034 -0.22229,0.22229 -1.47534,-1.88285 -2.78456,-4.67808 z m -117.6807,-16.51377 1.76374,-2.10183 -9.7709,-0.72295 c -5.37399,-0.39762 -10.52008,-0.78573 -11.43576,-0.86246 -1.48988,-0.12484 -1.51616,-0.42708 -0.25,-2.87556 4.96872,-9.60845 -3.56484,-6.52332 -10.35555,3.74383 -1.1722,1.7723 -1.19304,1.75475 -0.26952,-0.22699 0.8292,-1.77935 0.58048,-2.42324 -1.44992,-3.75361 -3.59981,-2.35869 -10.51014,-11.92286 -10.51014,-14.54646 0,-1.60349 -0.43862,-2.09781 -1.5,-1.69052 -2.82973,1.08587 -1.49707,-0.1733 2.92807,-2.7666 5.63449,-3.30204 7.40444,-3.40549 2.07193,-0.12111 -5.56486,3.42749 -3.93435,3.14057 2.59023,-0.4558 2.97537,-1.64004 4.95977,-2.99693 4.40977,-3.01531 -1.27531,-0.0426 1.65984,-1.97218 3,-1.97218 0.55,0 0.325,0.48556 -0.5,1.07902 -1.24752,0.8974 -1.19167,0.97898 0.33179,0.48469 1.00748,-0.32688 2.04654,-0.59433 2.30901,-0.59433 0.26248,0 0.19912,-0.45 -0.1408,-1 -0.33992,-0.55 -0.029,-1.00615 0.69098,-1.01366 0.71996,-0.008 2.20902,-0.25518 3.30902,-0.55037 1.66362,-0.44645 2.29777,0.3029 3.77045,4.45544 2.4375,6.87308 10.32518,15.25072 17.94267,19.05722 3.35848,1.67825 6.70945,3.05137 7.4466,3.05137 2.10298,0 1.56966,1.81135 -0.90972,3.08978 -1.2375,0.63808 -3.37178,2.11641 -4.74285,3.28518 -2.48554,2.11881 -2.48768,2.11888 -0.7291,0.0232 z m 192.44851,-13.72508 c 3.32192,-7.879 12.7063,-22.25538 15.88859,-24.3405 1.83779,-1.20417 3.99287,-1.47734 8.68411,-1.10077 8.98179,-0.0289 17.45553,-0.38071 18.83053,-0.78184 4.37875,-1.2774 -4.31509,3.09374 -11.62737,5.84608 -8.69028,3.27101 -21.0494,10.49001 -24.90353,14.54624 -1.63301,1.71863 -4.16468,3.98446 -5.62594,5.03517 l -2.65684,1.91038 z"
              id="path28"
              sodipodi:nodetypes="ssssssssscscsssssssssssssssscsssscssscscssssssssssssssssscssssssssssssssssscsssccssssssssssscsssssssssssscssssssssscccssssssssssssssssssssssssssssssssssscssssssssssssssssscsscsssssssssscsssssssssssscsscssssssscssssssssssssssssssssssssscssssssssssssscssscssssssccssssssssssssssssscssssssscsssscssscssscsssssssssssssssssssscsssssssscsssssssssssssssssssssssssssssssssssssssssssssscssssssssscssssssssssssssssssssscsssssssscssssssssssssssssssssssssssssssssssssscs" /><path
              style="display:inline;fill:#6fbabf"
              d="m 483.24054,905.32996 c 1.24391,-0.23919 3.04391,-0.23011 4,0.0202 0.95608,0.25029 -0.0617,0.44599 -2.26166,0.43489 -2.2,-0.0111 -2.98226,-0.21588 -1.73834,-0.45508 z m -126,-17 c 1.24391,-0.23919 3.04391,-0.23011 4,0.0202 0.95608,0.25029 -0.0617,0.44599 -2.26166,0.43489 -2.2,-0.0111 -2.98226,-0.21588 -1.73834,-0.45508 z m -17,-1 c 1.24391,-0.23919 3.04391,-0.23011 4,0.0202 0.95608,0.25029 -0.0617,0.44599 -2.26166,0.43489 -2.2,-0.0111 -2.98226,-0.21588 -1.73834,-0.45508 z m 23.02186,-9.03114 c 2.90594,-0.20264 7.40594,-0.20051 10,0.005 2.59406,0.20524 0.21648,0.37104 -5.28352,0.36844 -5.5,-0.003 -7.62242,-0.17054 -4.71648,-0.37318 z m 37.96648,0.0327 c 1.2375,-0.23835 3.2625,-0.23835 4.5,0 1.2375,0.23836 0.225,0.43337 -2.25,0.43337 -2.475,0 -3.4875,-0.19501 -2.25,-0.43337 z m 124.57007,-30.29426 c 0,-3.575 0.1815,-5.0375 0.40332,-3.25 0.22183,1.7875 0.22183,4.7125 0,6.5 -0.22182,1.7875 -0.40332,0.325 -0.40332,-3.25 z m -15.34896,-33.75 -6.47111,-6.75 6.75,6.47111 c 3.7125,3.55911 6.75,6.59661 6.75,6.75 0,0.73601 -1.01825,-0.20143 -7.02889,-6.47111 z m -142.97111,-18.75 c -2.42903,-2.475 -4.19142,-4.5 -3.91642,-4.5 0.275,0 2.48739,2.025 4.91642,4.5 2.42903,2.475 4.19141,4.5 3.91641,4.5 -0.275,0 -2.48738,-2.025 -4.91641,-4.5 z m -205.74782,-35 c 0.0111,-2.2 0.21589,-2.98226 0.45508,-1.73835 0.23919,1.24392 0.23011,3.04392 -0.0202,4 -0.25029,0.95609 -0.446,-0.0617 -0.4349,-2.26165 z m 329.02018,-10.5 c 0,-2.475 0.19502,-3.4875 0.43337,-2.25 0.23835,1.2375 0.23835,3.2625 0,4.5 -0.23835,1.2375 -0.43337,0.225 -0.43337,-2.25 z m 26.47764,-56.70577 c 1.2375,-0.23835 3.2625,-0.23835 4.5,0 1.2375,0.23836 0.225,0.43337 -2.25,0.43337 -2.475,0 -3.4875,-0.19501 -2.25,-0.43337 z m -166,-20 c 1.2375,-0.23835 3.2625,-0.23835 4.5,0 1.2375,0.23836 0.225,0.43337 -2.25,0.43337 -2.475,0 -3.4875,-0.19501 -2.25,-0.43337 z m 249.25,-56.79426 c 2.98742,-3.025 5.65668,-5.5 5.93168,-5.5 0.275,0 -1.94426,2.475 -4.93168,5.5 -2.98743,3.025 -5.65668,5.5 -5.93168,5.5 -0.275,0 1.94425,-2.475 4.93168,-5.5 z m 34.75,-32.20574 c 1.2375,-0.23835 3.2625,-0.23835 4.5,0 1.2375,0.23836 0.225,0.43337 -2.25,0.43337 -2.475,0 -3.4875,-0.19501 -2.25,-0.43337 z m 21.14788,-22.57088 c -11.00061,-7.37053 -15.65224,-24.6461 -14.60022,-54.22338 1.21765,-34.23411 8.78505,-57.89633 22.83261,-71.39449 12.93716,-12.43118 27.73857,-3.16855 31.44745,19.67962 2.58975,15.95387 1.30887,19.46219 -3.79063,10.38246 -2.49496,-4.44233 -3.1606,-4.93777 -8.28602,-6.16734 -5.18427,-1.24368 -5.76248,-1.19694 -8.28709,0.66997 -7.10374,5.2531 -12.21398,22.77775 -12.21398,41.88564 0,28.58009 3.91465,38.61713 16.6002,42.56241 l 4.1002,1.27518 -1.90201,3.22367 c -2.57182,4.35891 -10.15058,11.21426 -14.32666,12.95913 -4.76955,1.99285 -7.64255,1.78114 -11.57385,-0.85287 z M 444.80773,545.16849 c -19.79764,-7.37627 -30.27817,-25.94577 -31.99636,-56.69142 -2.50724,-44.865 22.89675,-81.04929 51.25049,-72.99886 23.30922,6.61815 36.65392,34.93587 35.13587,74.55902 -1.0195,26.61036 -10.75436,46.63078 -26.21885,53.92092 -7.06912,3.33247 -20.88258,3.92594 -28.17115,1.21034 z m 20.24189,-18.02408 c 16.8077,-7.63389 22.52047,-43.21285 10.87215,-67.71144 -5.98947,-12.59698 -13.88887,-17.40666 -22.92245,-13.9567 -11.61195,4.43464 -18.52044,19.9377 -18.52044,41.56096 0,29.56718 13.82958,47.71085 30.57074,40.10718 z M 213.97888,542.6384 c -12.72378,-4.024 -21.11078,-13.97601 -26.65294,-31.6264 -2.16916,-6.90821 -2.33928,-9.0665 -2.24434,-28.47477 0.0947,-19.35427 0.31047,-21.78302 2.75366,-30.99149 6.68427,-25.1933 17.43223,-37.75851 32.29765,-37.75851 15.67492,0 28.44951,16.95363 34.47269,45.75 1.71371,8.19314 1.68538,34.24903 -0.0468,43 -5.91727,29.89475 -22.14528,45.93129 -40.57996,40.10117 z m 14.56953,-21.28296 c 8.20115,-7.40924 10.99297,-17.01461 10.99297,-37.82175 0,-15.40286 -1.15819,-21.7197 -5.5915,-30.49646 -3.44798,-6.8261 -6.44927,-9.16667 -11.75429,-9.16667 -7.66636,0 -11.83214,4.3725 -16.49448,17.31299 -2.30163,6.38823 -2.6985,9.2599 -3.02294,21.87322 -0.53339,20.7365 2.48165,31.10307 11.29728,38.8433 5.0507,4.43458 9.23465,4.27821 14.57296,-0.54463 z m 402.23013,5.68179 c 0,-3.025 0.18747,-4.2625 0.41659,-2.75 0.22913,1.5125 0.22913,3.9875 0,5.5 -0.22912,1.5125 -0.41659,0.275 -0.41659,-2.75 z m -528.04748,-23.5 c 0.0111,-2.2 0.21589,-2.98226 0.45508,-1.73835 0.23919,1.24392 0.23011,3.04392 -0.0202,4 -0.25029,0.95609 -0.446,-0.0616 -0.4349,-2.26165 z m 595.11305,-20 c 0.003,-5.5 0.17053,-7.62242 0.37317,-4.71648 0.20264,2.90593 0.20051,7.40593 -0.005,10 -0.20525,2.59406 -0.37105,0.21648 -0.36844,-5.28352 z m -121.04516,-14.5 c 0,-3.575 0.1815,-5.0375 0.40332,-3.25 0.22183,1.7875 0.22183,4.7125 0,6.5 -0.22182,1.7875 -0.40332,0.325 -0.40332,-3.25 z m 44.64577,-118.75 -5.46584,-5.75 5.75,5.46584 c 5.34193,5.07793 6.20809,6.03416 5.46584,6.03416 -0.15629,0 -2.74379,-2.5875 -5.75,-5.75 z m 11.28634,-53.75 c 0.0111,-2.2 0.21589,-2.98226 0.45508,-1.73835 0.23919,1.24392 0.23011,3.04392 -0.0202,4 -0.25029,0.95609 -0.446,-0.0616 -0.4349,-2.26165 z m 30.99782,-13.22847 c 2.0625,-0.21575 5.4375,-0.21575 7.5,0 2.0625,0.21574 0.375,0.39226 -3.75,0.39226 -4.125,0 -5.8125,-0.17652 -3.75,-0.39226 z m -50.5,-51.00374 c 2.3375,-0.21053 6.1625,-0.21053 8.5,0 2.3375,0.21054 0.425,0.38279 -4.25,0.38279 -4.675,0 -6.5875,-0.17225 -4.25,-0.38279 z m -87.80384,-42.01779 -3.44616,-3.75 3.75,3.44616 c 3.49042,3.20762 4.20979,4.05384 3.44616,4.05384 -0.1671,0 -1.8546,-1.6875 -3.75,-3.75 z m -70.68241,-1.96759 c 1.52006,-0.22985 3.77006,-0.22371 5,0.0137 1.22994,0.23737 -0.0138,0.42543 -2.76375,0.41792 -2.75,-0.008 -3.75631,-0.20171 -2.23625,-0.43157 z m 70.73625,-7.78241 c 1.86578,-1.925 3.61733,-3.5 3.89233,-3.5 0.275,0 -1.02655,1.575 -2.89233,3.5 -1.86579,1.925 -3.61734,3.5 -3.89234,3.5 -0.275,0 1.02655,-1.575 2.89234,-3.5 z m 8.25218,-16 c 0.0111,-2.2 0.21589,-2.98226 0.45508,-1.73835 0.23919,1.24392 0.23011,3.04392 -0.0202,4 -0.25029,0.95609 -0.446,-0.0616 -0.4349,-2.26165 z m 173.20077,-1.75 -3.95295,-4.25 4.25,3.95294 c 3.95246,3.67621 4.70952,4.54706 3.95295,4.54706 -0.16338,0 -2.07588,-1.9125 -4.25,-4.25 z"
              id="path27"
              sodipodi:nodetypes="ssssssssssssssscccccssssssssscssssssssssssssssssssssssssssssssssssssssssscsssssssssssssssssssscssssssssssssssssssscscccssssscssssssssssssssssscssssscssssssssssssscsss" /><path
              style="display:inline;fill:#252324"
              d="m 232.27983,1091.0715 c -12.31416,-0.6597 -20.81346,-9.4154 -20.77986,-19.9322 0.05,-15.6506 10.06645,-31.3764 26.73739,-41.9776 3.00469,-1.9107 5.44695,-4.2612 5.69823,-5.4842 2.94808,-14.3483 3.07808,-19.6877 0.49579,-20.363 -2.72645,-0.7129 -15.03354,-6.83078 -17.04883,-8.47494 -2.9303,-2.39064 -4.90365,-7.62185 -4.90495,-13.00267 -0.003,-12.88932 7.07879,-57.23592 13.43125,-84.10604 2.0022,-8.46907 2.35994,-11.67351 1.65398,-14.81551 -0.56099,-2.49675 -0.51051,-6.63872 0.13381,-10.97984 0.9319,-6.27877 -0.27918,-24.29302 -1.73079,-25.74463 -0.2257,-0.2257 -2.5651,0.18765 -5.19867,0.91855 -3.20103,0.88839 -11.41809,1.35115 -24.7883,1.39601 -14.83015,0.0498 -20.62129,0.41599 -22.40352,1.41679 -4.09957,2.30211 -16.99709,0.89335 -29.09648,-3.17812 -25.12373,-8.45418 -35.24146,-14.18742 -41.97322,-23.78419 -5.31837,-7.58186 -6.30182,-8.17027 -19.026776,-11.38378 -38.677807,-9.76756 -63.789305,-23.58525 -73.228081,-40.29402 -2.025028,-3.58476 -4.83889,-6.56704 -8.852686,-9.38256 -16.3424975,-11.46361 -19.1154375,-26.83675 -7.4607525,-41.36232 1.765184,-2.2 4.478928,-6.18361 6.030541,-8.85247 C 18.404119,697.17404 31.679311,692.3199 60.297003,693.28168 l 15.369341,0.51654 -0.64683,-2.6305 c -0.35576,-1.44677 -0.90494,-6.35383 -1.2204,-10.90457 -1.11439,-16.07586 5.97018,-26.53176 19.37,-28.58758 10.775636,-1.65321 20.622726,3.97669 28.293566,16.17633 7.42441,11.80772 8.38207,12.72314 19.23836,18.38971 10.91815,5.69886 19.75916,12.92209 21.77608,17.79138 1.3008,3.14041 2.06214,3.46781 10.50176,4.5161 8.27004,1.02723 25.46908,6.33579 34.2666,10.57654 l 8.26661,3.98483 4.23339,-2.87802 c 2.32837,-1.58291 7.6084,-4.81524 11.7334,-7.18294 10.96393,-6.29317 41.18545,-26.40663 43.41053,-28.8912 1.0508,-1.17333 1.69157,-2.35231 1.42393,-2.61994 -0.26763,-0.26764 -8.3839,-2.5027 -18.03614,-4.96682 -70.10184,-17.89625 -111.22637,-45.67543 -137.30861,-92.75049 -4.30247,-7.76539 -5.57241,-9.21033 -11.64198,-13.24633 C 79.933324,551.0295 67.75615,488.86987 83.605454,439.2784 c 5.81789,-18.20382 14.20707,-31.43092 25.280936,-39.86009 6.33721,-4.82372 6.46767,-5.0221 11.10213,-16.88108 16.84081,-43.09351 40.40664,-75.63303 71.91119,-99.29442 3.87715,-2.91193 7.34507,-5.83693 7.70648,-6.5 0.89741,-1.64648 22.34562,-65.98623 22.36067,-67.07693 0.007,-0.47925 -3.7331,-2.18983 -8.31046,-3.8013 C 144.11651,181.38295 144.90434,84.657093 200.92915,40.0768 c 0,0 18.74427,-18.60677 37.69938,-24.277869 31.53927,-9.4360971 78.01056,0.440221 92.57322,11.322602 14.56266,10.882381 9.18333,9.830273 5.81755,13.311864 -6.23405,6.44855 -13.79719,-6.924138 -25.00631,-9.935238 -11.20911,-3.011099 -23.54595,-7.70691 -42.27246,-6.245528 -18.72651,1.461382 -28.84945,4.951018 -43.38064,14.440875 -10.24604,6.691359 -23.61859,17.497736 -34.01841,32.343724 -5.81213,8.296955 -10.49011,21.483711 -13.88571,35.5 -1.69382,6.99172 -1.36392,30.21771 0.53319,37.53891 3.39264,13.09263 9.59345,23.2135 20.14738,32.88426 13.26127,12.15154 35.34254,19.308 35.34254,11.45438 0,-0.75765 0.829,-2.20656 1.84223,-3.21979 1.72753,-1.72753 2.33458,-1.75455 9.75,-0.43397 20.14327,3.58723 53.95398,-3.79186 76.97795,-16.8002 l 5.07018,-2.86461 -2.70765,-8.27949 c -3.30398,-10.10301 -4.641,-21.0884 -2.50736,-20.60131 0.77533,0.177 4.93431,5.03017 9.24217,10.78482 12.33369,16.47593 25.30384,24.91616 52.33248,34.05495 6.875,2.32454 12.88329,4.57724 13.35175,5.006 1.30613,1.19541 -15.70267,2.31773 -24.28205,1.60224 -7.90155,-0.65897 -12.74438,-2.16028 -24.45365,-7.58081 -3.78617,-1.75272 -8.06117,-3.21814 -9.5,-3.25648 -1.43883,-0.0383 -8.24105,2.6614 -15.11605,5.99941 -25.01572,12.14588 -54.15828,18.82229 -70.65556,16.18684 -3.60056,-0.5752 -7.36076,-0.78736 -8.356,-0.47149 -3.59043,1.13956 -6.94019,9.19595 -12.05634,28.99634 -1.70536,6.6 -4.89907,17.55115 -7.09715,24.33588 -2.19808,6.78474 -3.77597,12.55643 -3.50643,12.82597 0.26954,0.26955 3.76487,-0.94724 7.7674,-2.70396 41.05515,-18.01921 101.50802,-23.26632 172.40408,-14.96409 89.30885,10.45845 176.60546,44.41053 224.37556,87.2659 2.95656,2.65238 5.61486,4.5832 5.90734,4.29072 0.53104,-0.53104 0.51416,-3.93455 -0.23246,-46.87512 l -0.39686,-22.8247 -3.32679,-5.57126 c -5.50011,-9.21084 -12.11177,-13.64065 -24.18242,-16.2022 -30.86714,-6.55041 -58.4358,-20.8189 -90.10973,-46.63737 l -6.96535,-5.67769 -6.53465,0.57567 c -3.59405,0.31662 -9.68464,1.03043 -13.53464,1.58624 -15.30004,2.20882 -34.36655,0.3366 -46.54104,-4.57005 l -4.45896,-1.79708 4,-0.7208 c 2.2,-0.39644 7.28023,-0.74674 11.28941,-0.77843 29.09994,-0.23007 49.98951,-6.72677 67.9409,-21.12978 10.0679,-8.07781 13.26969,-8.90228 13.26969,-3.41696 0,5.05934 -3.50486,11.77798 -8.58869,16.46409 -2.42622,2.23641 -4.41131,4.37491 -4.41131,4.75221 0,0.37731 1.6875,2.18967 3.75,4.02748 30.12475,26.84294 65.51692,43.25479 93.25,43.24135 9.80862,-0.005 23.60447,-2.28513 29.43927,-4.86614 6.83937,-3.02538 11.40244,-3.70594 12.1303,-1.80919 1.65669,4.31729 -7.08409,14.82692 -15.93155,19.15558 -3.10091,1.51713 -5.63802,2.95182 -5.63802,3.18818 0,0.23636 1.76222,3.67106 3.91605,7.63265 2.15383,3.96159 4.37726,9.0029 4.94097,11.2029 0.56371,2.2 1.57042,6.17484 2.23715,8.83299 1.49813,5.97286 2.97866,6.65902 15.90583,7.37168 8.0453,0.44353 10.49531,0.18836 16,-1.66645 17.73986,-5.97748 31.44813,-18.42551 39.8943,-36.22672 6.48719,-13.67243 8.49074,-40.24305 4.09536,-54.3115 -4.40359,-14.09475 -12.50753,-26.22247 -22.74633,-34.04037 -4.95184,-3.78101 -6.41268,-4.14743 -8.44254,-2.11757 -0.73813,0.73814 -2.42763,1.92304 -3.75442,2.63312 -2.14234,1.14654 -2.61575,1.03989 -4.22937,-0.95285 -0.99935,-1.23415 -1.817,-2.99187 -1.817,-3.90605 0,-0.91418 -1.10425,-6.01524 -2.45389,-11.33568 C 675.91732,121.6828 668.01983,107.64301 653.53446,92.946388 626.42642,65.443026 591.96378,56.118561 563.66917,68.63178 554.66158,72.615361 552.82527,71.948887 547.26715,62.678785 540.09895,50.723338 533.43807,43.329564 529.25081,38.817605 525.06356,34.305647 515.77214,22.452379 491.89175,5.8257629 468.01136,-10.800853 427.34698,-9.5658822 401.58494,1.2276758 375.8229,12.021234 365.12557,21.084365 337.20094,40.331944 325.18781,31.47776 323.07697,36.826983 334.12623,27.521811 c 11.04925,-9.305171 35.63191,-28.29556993 75.3461,-44.37158 39.71419,-16.07601 55.22337,-6.64 82.2682,5.677743 27.04482,12.3177436 57.98835,50.23547 57.98835,50.23547 l 3.75,4.057174 7.5,8.114348 6.14434,-1.598863 c 7.94595,-2.067673 29.64278,-2.149107 39.04028,-0.14653 39.3023,8.375195 69.92964,35.123232 86.82732,75.829637 2.60076,6.26522 3.35521,7.14038 9.90166,11.48594 38.66788,25.66787 52.74066,78.05771 32.16203,119.73209 -8.15565,16.51626 -22.48259,30.78124 -39.07563,38.90667 -12.45616,6.09963 -21.04326,7.96475 -34.01421,7.38787 -9.85271,-0.4382 -10.37707,-0.35982 -10.85452,1.62251 -0.87908,3.64987 -1.93642,46.14284 -1.24046,49.8526 0.36784,1.96078 2.96221,7.0782 5.76525,11.37203 5.50946,8.43965 11.52485,20.01565 14.35587,27.62644 1.50076,4.03456 2.82423,5.46656 8.60253,9.30797 15.93993,10.59684 26.16671,28.4647 31.49317,55.0237 2.47299,12.3309 2.49577,43.05576 0.0417,56.22796 -7.9783,42.8233 -32.80835,76.49675 -62.47323,84.72351 -5.82183,1.61452 -6.85396,2.35301 -13,9.3015 -17.9777,20.32493 -27.28957,28.89564 -42.43109,39.0539 -10.43792,7.00267 -33.56927,18.08054 -48.1444,23.05691 -17.37838,5.93348 -18.27468,7.88889 -6.32918,13.80812 8.13123,4.02918 14.59782,9.03988 33.72858,26.13488 38.45796,34.36554 52.07995,50.44344 65.08259,76.81627 5.23161,10.61113 5.92099,11.52587 10.96533,14.55007 12.14916,7.28371 22.13501,29.0474 23.67707,51.60307 0.49087,7.17986 1.05626,9.17911 3.95927,14 7.01645,11.65187 11.77134,32.26224 11.11629,48.18427 -0.75605,18.37731 -7.28136,28.64982 -23.21165,36.54107 -4.9989,2.47626 -12.14295,6.34345 -15.87568,8.59375 -5.88159,3.54575 -7.88221,4.19427 -15,4.86233 -5.61437,0.52696 -10.11176,1.65137 -14.21322,3.55352 -16.46185,7.63455 -32.82344,1.19523 -38.56804,-15.17897 l -2.43196,-6.93195 -5.5247,-0.59424 c -6.73248,-0.72415 -9.20331,-1.93497 -14.84057,-7.27256 l -4.36526,-4.1332 0.52773,5 c 0.29026,2.75 1.94713,11.525 3.68196,19.5 1.73482,7.975 4.46025,21.925 6.05651,31 1.64596,9.35758 4.25788,20.17936 6.0339,24.99996 2.9716,8.0657 3.10765,9.0621 2.66267,19.5 -0.42938,10.072 -0.25748,11.5062 2.03752,17 6.90227,16.5228 7.00836,29.244 0.30375,36.4213 0,0 -11.78456,9.6679 -19.08028,12.8995 -7.29572,3.2316 -9.98336,4.9 -27.34382,7.304 -17.36047,2.404 -78.40822,-17.1468 -81.03453,-19.2131 -4.48981,-3.5317 -5.89085,-7.5732 -6.56829,-18.9473 -0.60192,-10.106 -0.77237,-10.7252 -4.18129,-15.1898 -3.3842,-4.4321 -3.6314,-5.3095 -5.17992,-18.3848 -0.89437,-7.5518 -2.00846,-14.1129 -2.47576,-14.5802 -0.4673,-0.4673 -10.71813,-0.7281 -22.77963,-0.5796 l -21.92999,0.27 -0.67008,10.0908 c -0.38735,5.8333 -1.38344,11.7402 -2.36083,14 -1.24665,2.8824 -1.72732,7.1058 -1.82993,16.0789 -0.13157,11.5061 -0.27549,12.3157 -2.63916,14.845 -6.21561,6.6515 -23.2066,11.0494 -46.08742,11.9293 -6.27692,0.2414 -1.15345,-0.375 -14.90598,1.4974 -12.62334,1.7186 -41.01738,3.2994 -54.70736,2.566 z m 217.69905,-83.9845 c 21.68742,-2.3488 28.45067,-3.3032 39.83393,-5.6209 14.99694,-3.05358 19.89947,-5.16388 21.61447,-9.3043 1.5872,-3.83184 1.09992,-16.18179 -1.87906,-47.62457 -1.14639,-12.1 -2.37198,-26.35526 -2.72354,-31.67835 -0.35156,-5.32308 -0.95406,-10.18781 -1.33891,-10.8105 -0.50529,-0.81759 -2.94207,-0.55964 -8.76992,0.92834 -8.32301,2.12506 -16.39541,2.76948 -14.68697,1.17246 0.5225,-0.48842 4.47113,-2.08777 8.77473,-3.5541 10.48187,-3.57142 24.19732,-9.58428 29.46151,-12.91596 7.1424,-4.52039 10.31269,-12.38917 6.88188,-17.08109 -1.26783,-1.73386 -2.02527,-1.59979 -15.75,2.7877 -46.29648,14.79995 -116.21121,19.22787 -190.91812,12.09144 -28.18548,-2.69244 -55.39371,-8.13373 -65.12821,-13.02478 -2.54551,-1.27898 -4.90801,-2.04561 -5.25,-1.70363 -0.34198,0.34199 -0.62179,3.21218 -0.62179,6.37821 0,7.09115 3.02011,10.65692 12.70739,15.00331 11.4189,5.12331 38.23477,12.47496 54.25062,14.87296 10.79248,1.61593 10.0983,2.37769 -1.45801,1.59997 -14.94132,-1.00554 -34.23228,-5.04208 -50.48957,-10.56471 -7.96927,-2.70717 -14.81078,-4.60093 -15.20336,-4.20835 -1.67844,1.67844 -9.83471,46.68265 -12.85936,70.95463 -2.63173,21.1189 -2.86936,20.72004 16.28031,27.32674 25.05177,8.64288 45.21541,11.49368 112.77198,15.94398 16.1725,1.0653 71.58557,0.4302 84.5,-0.9685 z m 162,-49.94971 c 10.50041,-4.87222 16.5,-16.88115 16.5,-33.02684 0,-6.2144 2.17037,-10.03362 4.01018,-7.05675 2.56386,4.14841 2.24426,20.54272 -0.59293,30.41503 -0.77949,2.71232 -1.41725,5.1249 -1.41725,5.36129 0,0.23638 1.67195,0.11613 3.71544,-0.26723 11.04944,-2.07289 20.1743,-13.42849 22.34428,-27.80674 1.81071,-11.99772 2.00973,-12.71882 3.51034,-12.71882 2.66784,0 3.65667,5.04738 3.13455,16 l -0.50054,10.5 3.26515,-3.76298 c 5.34804,-6.16341 6.79573,-12.59485 6.30312,-28.00191 -0.37092,-11.6011 -0.80481,-14.31503 -3.51436,-21.98215 -8.20997,-23.23143 -19.34542,-31.01499 -39.75798,-27.79041 -23.27072,3.67609 -42.825,11.6414 -52.23414,21.27722 -3.40133,3.48328 -3.60173,3.99276 -2.3555,5.9883 0.74916,1.1996 0.96331,2.33366 0.47588,2.52013 -9.38427,3.58993 -12.11145,6.78423 -14.97494,17.53991 -3.15715,11.8587 -1.3778,19.72274 6.00163,26.52487 5.50864,5.07769 9.77714,5.58085 14.39323,1.69666 2.57627,-2.16779 3.01433,-3.30113 3.38206,-8.75 0.44323,-6.56762 2.978,-13.22572 5.38703,-14.15015 4.57965,-1.75737 7.03096,14.91402 3.88405,26.41551 -2.88272,10.53591 -2.95925,14.0697 -0.37582,17.35399 4.37759,5.56521 12.20797,7.06586 19.41652,3.72107 z m -63.05547,-58.22525 c 1.49702,-5.041 1.4721,-5.24984 -1.51695,-12.71464 -3.59672,-8.98239 -4.86849,-10.8579 -6.074,-8.95751 -1.78071,2.80717 -0.64893,10.3536 3.75651,25.04734 0.92272,3.07761 2.27047,1.89128 3.83444,-3.37519 z m 32.8615,-36.72494 c 18.60347,-10.57937 36.98046,-15.53089 55.14463,-14.85824 6.62713,0.24541 13.94766,1.04438 16.26782,1.77547 2.32017,0.73109 4.53015,1.0176 4.91106,0.63668 1.96495,-1.96495 -6.28636,-26.61858 -10.70177,-31.97524 -1.74615,-2.11838 -5.86632,-5.06253 -9.72838,-6.95161 -6.63836,-3.24708 -6.83149,-3.27679 -21.19939,-3.26133 -13.2014,0.0142 -15.25573,0.26727 -22.93834,2.82576 -19.0122,6.33149 -34.80823,17.73952 -41.21611,29.7666 -1.9883,3.73188 -2.33626,5.81996 -2.31497,13.89204 0.0261,9.90103 2.0259,16.35528 7.41055,23.91733 l 2.00837,2.82049 7.35349,-7.11891 c 4.04442,-3.9154 10.79579,-9.07647 15.00304,-11.46904 z M 430.5017,877.53823 c 30.65774,-2.48716 54.455,-6.54641 75.68556,-12.91017 19.17331,-5.74711 20.71854,-7.2342 20.06132,-19.30655 -0.72853,-13.38213 -3.12138,-18.4877 -14.12264,-30.13309 -9.57153,-10.13197 -15.12102,-17.8302 -16.61923,-23.05415 -1.61391,-5.62738 2.00343,-3.60784 12.49093,6.97362 18.40904,18.57397 24.36151,22.32463 24.48702,15.42934 0.0745,-4.09276 4.90391,-15.59519 9.36566,-22.30662 8.59076,-12.92235 23.60184,-24.78915 30.3723,-24.01041 2.55248,0.29359 2.05388,0.88152 -6.74374,7.95209 -14.42026,11.58942 -23.26795,23.15826 -27.67633,36.18834 -3.0361,8.97394 -2.97042,9.82732 1.28719,16.7252 l 3.61086,5.85006 8.02085,-8.47347 c 17.28665,-18.26216 39.70865,-28.60092 64.82637,-29.89137 5.06187,-0.26006 9.43488,-0.70431 9.71778,-0.98722 0.28291,-0.28291 -2.01069,-5.58413 -5.0969,-11.78049 -9.04431,-18.15881 -24.6603,-36.10688 -53.68982,-61.70779 -26.2545,-23.15364 -33.38351,-27.73869 -44.72861,-28.76743 -7.66028,-0.69461 -9.12988,0.16583 -15.35988,8.99314 -10.74404,15.22327 -14.77505,26.18024 -15.62298,42.46597 -0.32216,6.1875 -0.90673,11.25 -1.29904,11.25 -1.21566,0 -2.05697,-12.40993 -1.36558,-20.14321 0.78092,-8.73468 4.26651,-19.1457 9.09494,-27.16544 1.92346,-3.19475 3.29466,-6.01117 3.04712,-6.25872 -0.24754,-0.24754 -5.34884,1.84322 -11.33621,4.64613 -32.87262,15.38889 -47.48378,20.53337 -67.94939,23.92449 -6.47167,1.07234 -11.94458,2.12762 -12.16203,2.34507 -0.21744,0.21744 2.1046,2.95595 5.16009,6.08558 10.88449,11.14857 12.994,30.29107 14.67764,46.43749 0.93023,8.9211 -0.82515,24.29379 -2.64193,26.77838 -2.90714,3.97575 -22.78938,2.28079 -32.88354,-2.04593 -15.78511,-6.76606 -31.82618,-19.48431 -37.77702,-35.03482 -2.52346,-6.5942 -3.18711,-6.79575 -5.76138,-1.74975 -5.75625,11.28319 -17.86238,21.19619 -33.2834,27.25385 -6.9495,2.72989 -10.10771,3.35898 -19.30475,3.84536 l -10.99595,0.58152 -1.7571,11.5 c -2.1034,13.76653 -2.59385,39.61461 -0.86812,45.75267 0.66214,2.35511 2.208,4.91599 3.46446,5.73925 4.0497,2.65347 30.05468,7.06355 50.65266,8.58997 7.425,0.55023 14.175,1.1547 15,1.34326 4.6785,1.06932 61.78873,0.39287 78.02282,-0.92415 z m -31.70759,-23.60288 c -8.87722,-2.56859 -13.70014,-14.25448 -9.7675,-23.6666 5.78868,-13.85426 22.47643,-14.57519 29.20227,-1.26156 7.1845,14.22154 -4.5259,29.24198 -19.43477,24.92816 z m -99.05417,-8.82397 c -12.67142,-9.17913 -7.25536,-29.66499 8.1473,-30.81654 6.69187,-0.50031 11.25748,1.98865 14.39547,7.84772 8.4653,15.80596 -8.52836,33.1208 -22.54277,22.96882 z m -22.46346,20.17585 c -1.81842,-4.33751 -1.4227,-31.95148 0.62696,-43.75 0.95547,-5.5 2.1065,-17.2 2.55785,-26 1.63708,-31.91858 10.62242,-52.07808 28.50123,-63.94536 3.034,-2.01386 5.51636,-3.82633 5.51636,-4.02773 0,-0.2014 -2.3625,-1.1409 -5.25,-2.08779 -11.25048,-3.68931 -18.29268,-10.22256 -22.83981,-21.18912 -1.19727,-2.8875 -2.6026,-5.25 -3.12296,-5.25 -2.09033,0 -14.70361,5.51524 -21.28723,9.30798 -11.98052,6.90182 -32.50061,20.81917 -32.48762,22.03409 0.007,0.63686 1.52404,2.1367 3.37163,3.33297 6.8169,4.4138 13.54313,14.88348 14.98507,23.32496 0.56309,3.29642 -1.00072,7.67339 -1.96504,5.5 -3.71784,-8.37934 -9.6496,-17.46691 -13.13788,-20.12751 -4.40473,-3.3596 -12.90831,-7.06368 -13.91204,-6.05995 -0.33071,0.33071 0.528,3.43318 1.90825,6.89438 10.21752,25.62199 4.52949,62.21885 -13.31155,85.64668 -2.24232,2.94448 -3.80503,5.62553 -3.47268,5.95787 2.27326,2.27326 23.51575,-0.62528 29.68825,-4.05096 4.75192,-2.63727 6.08659,-5.55426 9.72777,-21.26051 7.87902,-33.98617 26.18899,-75.32571 37.94508,-85.67096 3.55324,-3.12682 3.67023,-3.15625 3.98334,-1.00213 0.17742,1.22062 -0.87873,4.3464 -2.34699,6.94619 -11.1869,19.80822 -28.27759,75.10714 -32.05784,103.7269 -2.87124,21.73773 -0.7668,26.24561 14.72606,31.54446 4.5959,1.57188 9.35755,2.87992 10.58144,2.90675 2.0437,0.0448 2.13119,-0.17557 1.07235,-2.70121 z m -96.41295,-27.66525 c 9.27266,-2.79066 19.43187,-13.85013 26.71719,-29.08475 5.69529,-11.90963 8.10883,-21.97136 8.65858,-36.09642 0.53349,-13.70748 -0.58211,-19.12797 -6.2554,-30.39361 -4.46862,-8.87349 -7.86125,-12.25127 -15.00502,-14.9393 -7.60736,-2.86247 -24.21749,-6.68652 -25.12064,-5.78337 -0.37658,0.37658 0.0745,3.95349 1.00251,7.94869 4.96177,21.36216 4.70099,36.9718 -0.89289,53.44821 -6.16386,18.15523 -20.90119,32.84917 -37.40267,37.29255 -3.69477,0.9949 -4.15051,1.38245 -2.97477,2.52963 2.53053,2.46905 36.17755,14.45391 45.97953,16.37761 0.32508,0.0638 2.7072,-0.52086 5.29358,-1.29924 z m -42.88465,-33.47605 c 10.85533,-6.23644 18.68347,-17.27851 22.45348,-31.672 3.02048,-11.53187 2.67714,-18.4367 -0.91677,-18.4367 -1.54476,0 -1.81536,-1.49096 -2.3141,-12.75 -0.67615,-15.26399 -3.7407,-26.08771 -9.57716,-33.82574 -3.01579,-3.99834 -5.58803,-5.87648 -13.67981,-9.98841 -11.6587,-5.92448 -16.56706,-10.47285 -23.03411,-21.34472 -5.11783,-8.60367 -8.64805,-11.36386 -14.534076,-11.36386 -8.11382,0 -12.28569,6.67907 -9.96327,15.95096 2.52509,10.08102 4.46668,12.97453 11.95791,17.82058 10.385856,6.71858 12.860126,11.65176 7.579426,15.1118 -2.4808,1.62549 -2.59082,1.5846 -5.83543,-2.16886 l -3.306946,-3.82558 -14.91457,-0.94328 c -37.176135,-2.35123 -50.865916,-0.68402 -58.051967,7.06986 -3.349475,3.61414 -3.167288,6.33232 0.387395,5.77982 12.146362,-1.88787 23.653265,-1.17207 41.711746,2.59474 l 9.961746,2.07792 1.66411,-2.05509 c 2.23773,-2.76348 2.91237,-1.7729 2.91257,4.27652 2.5e-4,7.45269 2.68006,20.20511 5.57449,26.52739 1.38998,3.03611 2.79677,7.48673 3.12622,9.89027 0.53654,3.91452 0.40092,4.33206 -1.30095,4.00539 -1.04915,-0.20138 -3.10629,-2.59603 -4.59395,-5.34766 -1.4817,-2.74063 -3.66084,-5.76585 -4.84251,-6.72272 -5.12255,-4.14799 -26.97918,-8.49759 -36.636821,-7.29094 -7.251382,0.906 -12.326651,5.32216 -12.326651,10.72582 0,11.40698 17.543186,24.24504 47.411112,34.69534 19.34526,6.76859 27.895256,8.32824 43.338886,7.90566 7.2875,-0.19941 13.25,-0.21481 13.25,-0.0342 0,0.18058 -0.68656,1.65598 -1.52568,3.27867 -1.98842,3.84517 -0.58844,3.85889 6.02568,0.059 z m 280.89911,-0.3587 c 1.45174,-5.16532 1.84424,-21.95636 0.68813,-29.43763 -2.3826,-15.41795 -5.413,-21.75473 -14.2761,-29.85229 -7.68573,-7.02189 -17.6947,-11.73674 -25.18122,-11.86193 -8.66836,-0.14495 -9.36452,1.6787 -3.24085,8.48969 5.53925,6.16098 6.62411,8.09933 10.66129,19.04875 4.52485,12.27206 2.79951,11.92313 -8.09233,-1.63659 -3.97609,-4.95 -8.85573,-10.70963 -10.84364,-12.79917 l -3.61439,-3.79917 -1.75,2.15995 c -1.37193,1.69331 -1.75,4.07059 -1.75,11.00368 0,18.60478 8.16545,31.5191 27.2279,43.06312 8.3601,5.06279 16.57035,7.74779 23.90547,7.81781 5.01061,0.0478 5.70327,-0.19495 6.26574,-2.19622 z M 310.47888,793.47481 c 16.86503,-6.68263 30.89698,-22.57051 35.42106,-40.10605 1.63466,-6.336 0.96839,-11.33153 -1.51131,-11.33153 -0.62687,0 -4.90304,3.56226 -9.5026,7.91613 -17.87692,16.92199 -19.13784,15.56355 -4.18071,-4.50403 4.57979,-6.14458 6.77356,-9.99272 6.77356,-11.88167 0,-2.69545 -0.14491,-2.77434 -4.1176,-2.24149 -5.92264,0.7944 -15.35189,5.75141 -23.07208,12.12912 -11.63618,9.61276 -19.20971,25.81316 -20.48831,43.82619 l -0.64293,9.05768 8.41046,-0.54063 c 4.62575,-0.29735 10.43546,-1.34302 12.91046,-2.32372 z M 24.478882,747.60942 c 4.920227,-6.63789 11.371854,-9.57743 20.993398,-9.56519 6.401283,0.008 19.364705,2.92748 26.398137,5.9448 2.690347,1.15415 5.099037,1.89096 5.352647,1.63735 0.63724,-0.63725 -0.69966,-9.89907 -1.6511,-11.43855 -1.50029,-2.42751 -24.599385,-6.39173 -38.093082,-6.53746 -10.229764,-0.11047 -20.304485,2.01293 -23.47006,4.94669 -4.3588235,4.03962 -3.203655,10.45372 2.820928,15.66326 4.02278,3.47856 4.626298,3.4272 7.649132,-0.6509 z M 366.76422,727.92605 c 13.80142,-9.43249 20.818,-23.53505 15.75396,-31.66375 -2.95086,-4.73666 -5.83267,-5.46011 -20.0393,-5.03064 -14.53892,0.43951 -17.8237,1.7442 -21.02124,8.34945 -2.3922,4.94161 -2.65597,14.89957 -0.52948,19.98898 4.27417,10.22953 17.04922,14.36126 25.83606,8.35596 z m 42.97971,-5.89032 c 21.08837,-2.05137 50.31956,-11.99958 81.23495,-27.64656 18.80251,-9.51639 22.5,-11.8039 22.5,-13.92004 0,-1.77685 1.31022,-1.87482 -13.74347,1.0277 -12.26815,2.36545 -23.35261,3.64859 -61.25653,7.0911 -18.13386,1.64695 -28.59907,3.41928 -34.3305,5.81403 -3.85355,1.61011 -7.6695,6.02203 -7.6695,8.86729 0,1.89824 0.53063,1.93924 16.25,1.25569 11.04837,-0.48043 15.9299,-0.37991 15.25,0.31404 -0.92068,0.93971 -21.23457,6.16275 -29.58728,7.60738 -2.02101,0.34955 -5.47392,2.23534 -7.90835,4.31912 l -4.32108,3.69868 2.90835,1.24143 c 3.52595,1.50505 7.88036,1.57458 20.67341,0.33014 z m -80.26505,-4.276 c 0,-2.61152 -3.30766,-5.84909 -8.82212,-8.63517 -4.92013,-2.48581 -10.679,-7.08733 -8.86989,-7.08733 0.33988,0 2.90129,0.9 5.69201,2 7.34667,2.89578 10.61297,2.6822 11.46932,-0.75 0.37738,-1.5125 1.75181,-4.876 3.0543,-7.47443 1.30248,-2.59844 2.055,-4.89249 1.67227,-5.0979 -0.38274,-0.20541 -4.74589,-1.66961 -9.69589,-3.25378 -11.47038,-3.67093 -24.75722,-4.15314 -27.07865,-0.98276 -4.2132,5.75394 1.94442,19.69869 11.79602,26.71365 5.69356,4.05417 20.78263,7.37053 20.78263,4.56772 z m 97,-43.73686 c 84.00315,-5.11687 145.63254,-26.11075 178,-60.63522 4.125,-4.3999 8.84133,-9.94021 10.48073,-12.31182 l 2.98073,-4.312 -3.48073,-2.59068 c -10.40306,-7.7429 -20.53246,-24.73642 -24.8181,-41.63592 -9.76028,-38.48766 -2.0189,-98.92272 16.91036,-132.01501 9.08603,-15.88428 19.82427,-24.68445 34.92701,-28.62329 4.675,-1.21925 8.66575,-2.34017 8.86833,-2.49093 0.60967,-0.45371 -6.2419,-12.39577 -11.40055,-19.87077 -6.31008,-9.14347 -25.71955,-28.56406 -36.43893,-36.45981 -19.48531,-14.3526 -57.67463,-34.531 -84.02885,-44.39899 -55.91439,-20.93641 -116.41555,-31.62921 -179,-31.63593 -59.88854,-0.006 -94.43755,8.59141 -130.5,32.47616 -48.56295,32.16403 -78.09612,84.37107 -89.67288,158.51857 -2.05525,13.16354 -1.73263,61.43049 0.50464,75.5 3.20802,20.17425 8.6973,39.26561 15.29072,53.18013 10.45143,22.0563 38.33402,47.92924 67.95463,63.05672 35.37979,18.06876 84.36815,29.99651 136.42289,33.21642 9.625,0.59536 19.3,1.22379 21.5,1.3965 9.86153,0.77418 50.52358,0.54813 65.5,-0.36413 z M 318.0858,612.3962 c -9.82809,-2.10729 -19.16265,-6.9477 -25.85692,-13.40804 -5.19176,-5.01032 -5.75,-5.9535 -5.75,-9.71481 0,-3.09511 0.49713,-4.43181 1.93426,-5.20094 3.37557,-1.80655 4.93838,-1.16824 11.17313,4.56354 9.63367,8.85651 16.94974,11.78576 30.5702,12.23987 16.83361,0.56123 28.7009,-4.17065 38.56538,-15.37729 4.22044,-4.79467 6.8148,-5.50999 10.53452,-2.9046 5.43041,3.8036 1.47332,13.42729 -8.14659,19.81263 -15.76985,10.46743 -34.41383,13.97993 -53.02398,9.98964 z m 130.39308,-27.47642 c -18.8684,-1.89058 -32.80688,-6.57041 -61.18392,-20.54241 -31.39625,-15.45856 -42.44795,-18.5338 -66.81608,-18.59221 -21.67751,-0.052 -30.33173,2.17701 -54.5,14.03696 -24.55435,12.0494 -33.86658,14.52715 -55,14.63412 -17.95633,0.0909 -23.87793,-1.0295 -36,-6.81132 -22.69227,-10.82346 -37.45291,-32.27792 -44.10601,-64.10769 -2.63357,-12.59955 -2.68392,-48.81789 -0.0862,-62 11.06908,-56.16973 44.34989,-91.5 86.1922,-91.5 13.76591,0 25.00236,3.26555 44,12.78733 17.0297,8.53544 42.34037,13.21267 71.5,13.21267 31.77142,0 51.36543,-3.80015 79.06324,-15.33387 17.30195,-7.20475 22.982,-9.08123 33.91807,-11.20525 35.99431,-6.99088 71.78658,3.44138 96.57206,28.14756 30.70359,30.60536 42.51183,84.03546 28.9223,130.86813 -15.11369,52.0853 -63.56125,82.30912 -122.47567,76.40598 z m 20.75316,-39.40189 c 25.73377,-7.64211 37.77389,-52.62602 25.55577,-95.48066 -9.81812,-34.43671 -41.41629,-46.63689 -62.94126,-24.30188 -19.62904,20.36772 -25.51188,67.50133 -11.94541,95.70718 9.82199,20.42076 29.42373,29.98715 49.3309,24.07536 z m -18.44056,-18.55498 c -10.93265,-5.41161 -16.85351,-21.25164 -16.09394,-43.05602 0.64712,-18.57639 5.10476,-29.63632 14.64809,-36.34362 13.80855,-9.70502 27.39684,3.09816 31.81198,29.97396 3.78609,23.04659 -4.83284,46.67185 -18.4014,50.43987 -5.36413,1.48963 -7.22514,1.33188 -11.96473,-1.01419 z m -220.6756,15.15183 c 22.33351,-10.14133 32.5627,-52.16186 22.30378,-91.62167 -5.41072,-20.8117 -16.93015,-34.98207 -30.03535,-36.94732 -14.9562,-2.24282 -28.99798,13.58451 -35.03626,39.49148 -3.11036,13.34487 -3.37973,47.03416 -0.45575,57 7.33059,24.98492 26.88484,39.49671 43.22358,32.07751 z m -14.73912,-19.35413 c -8.81339,-5.90507 -12.90629,-17.63721 -12.8831,-36.9289 0.0217,-18.02572 3.90621,-31.56163 11.1309,-38.78632 3.71817,-3.71817 11.00336,-4.25197 15.01063,-1.09985 7.15459,5.6278 10.84374,18.6363 10.84355,38.23603 -2e-4,20.97518 -4.25991,33.38463 -13.36569,38.93706 -4.09867,2.49924 -6.59608,2.41596 -10.73629,-0.35802 z m 431.22639,60.73772 c 4.85383,-1.70703 11.87573,-6.0324 11.87573,-7.31523 0,-0.45417 -1.9125,-1.44766 -4.25,-2.20777 -17.00143,-5.52853 -25.91702,-34.79043 -22.10926,-72.56492 1.55893,-15.46523 1.92143,-17.53071 5.46907,-31.16255 7.27848,-27.96769 17.98056,-43.87879 33.50019,-49.80579 2.4145,-0.92211 4.39,-1.96998 4.39,-2.32861 0,-1.13825 -12.31747,-8.66027 -17.03343,-10.40196 -26.36256,-9.73614 -49.16374,19.71411 -57.54499,74.32573 -2.16333,14.0961 -2.18254,42.34737 -0.0378,55.60789 5.59575,34.59763 24.33369,53.38174 45.7405,45.85321 z m 21.29741,-20.86393 c 10.60742,-4.43207 21.35038,-21.06935 26.21425,-40.59717 6.16578,-24.75483 5.29262,-64.14734 -1.74543,-78.74505 -6.60016,-13.68947 -17.92,-16.60482 -27.76023,-7.14944 -14.04756,13.49816 -21.61496,37.16038 -22.83261,71.39449 -0.8413,23.65289 1.49953,37.50805 7.96166,47.12433 5.68162,8.45479 11.20545,10.87962 18.16236,7.97284 z M 108.28215,541.28723 c -7.05115,-34.11937 -6.94283,-76.93853 0.28492,-112.62273 2.20835,-10.90288 1.88322,-12.56718 -1.60289,-8.20484 -13.736706,17.18945 -20.888196,62.5145 -14.589226,92.46431 2.66255,12.65965 6.97015,24.11302 11.863466,31.54343 5.26022,7.98755 6.19908,7.24919 4.04373,-3.18017 z"
              id="path25"
              sodipodi:nodetypes="sssssssssssssssssssssscsssssssscsssssssssssscssszazzssssssssscssssssssssssssssssscssscssscssssssscsssssssssssssssssssszzzczzzcccsssssssssssssssssssssssssssssscsscssssssszzcssssscsssssssssssssssssssssssssssssssssssssssssscssssssssssssssssssssssssssssssscsssssssssssssscssssssssssssssssasasssscssssssssssssssssssssssscssssssssssssssssssssssscssssssssssssssssscsssscssssssssssssssssssssssscsssssssssssssscsssssssssssssssssssssssssssscsssssssssssssssscsssssssscssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss" /><path
              style="display:inline;fill:#a44538;stroke-width:0.999516;stroke-dasharray:none"
              d="m 401.20678,804.08483 c -11.05287,-3.53266 -26.17551,-14.314 -32.13004,-22.9064 -4.99693,-7.21057 -7.59786,-16.13698 -7.59786,-26.07591 0,-6.93309 0.37807,-9.31037 1.75,-11.00368 l 1.75,-2.15995 3.61439,3.79917 c 1.98791,2.08954 6.86755,7.84917 10.84364,12.79917 10.89184,13.55972 12.61718,13.90865 8.09233,1.63659 -4.03718,-10.94942 -5.12204,-12.88777 -10.66129,-19.04875 -6.12367,-6.81099 -5.42751,-8.63464 3.24085,-8.48969 10.38269,0.17362 26.38374,10.0964 32.83478,20.36193 5.19695,8.2699 8.15213,24.2203 7.1983,38.85229 -0.93895,14.40373 -0.82925,14.19781 -7.52963,14.13385 -3.09836,-0.0296 -8.23082,-0.88396 -11.40547,-1.89862 z M 289.80089,787.28148 c 1.2786,-18.01303 8.85213,-34.21343 20.48831,-43.82619 7.72019,-6.37771 17.14944,-11.33472 23.07208,-12.12912 3.97269,-0.53285 4.1176,-0.45396 4.1176,2.24149 0,1.88895 -2.19377,5.73709 -6.77356,11.88167 -14.95713,20.06758 -13.69621,21.42602 4.18071,4.50403 4.59956,-4.35387 8.87573,-7.91613 9.5026,-7.91613 2.4797,0 3.14597,4.99553 1.51131,11.33153 -3.55255,13.76985 -13.38082,27.24162 -25.42534,34.85095 -8.23611,5.20329 -13.60973,6.98123 -22.90618,7.57882 l -8.41046,0.54063 z m 61.17799,-57.59349 c -7.72896,-3.57644 -11.5,-10.07014 -11.5,-19.80292 0,-7.64632 2.63875,-13.60864 7.22876,-16.33353 3.88686,-2.30747 26.24712,-3.48309 31.02078,-1.63096 9.52165,3.69431 8.00849,18.78301 -2.92956,29.21254 -9.50445,9.06257 -16.94661,11.7354 -23.81998,8.55487 z m 38.09164,-7.9824 -2.90835,-1.24143 4.32108,-3.69868 c 2.43443,-2.08378 5.88734,-3.96957 7.90835,-4.31912 8.35271,-1.44463 28.6666,-6.66767 29.58728,-7.60738 0.6799,-0.69395 -4.20163,-0.79447 -15.25,-0.31404 -15.71937,0.68355 -16.25,0.64255 -16.25,-1.25569 0,-2.84526 3.81595,-7.25718 7.6695,-8.86729 5.73143,-2.39475 16.19664,-4.16708 34.3305,-5.81403 37.90392,-3.44251 48.98838,-4.72565 61.25653,-7.0911 15.05369,-2.90252 13.74347,-2.80455 13.74347,-1.0277 0,5.29342 -58.36712,31.87175 -83.60519,38.07086 -16.30806,4.00568 -35.37039,5.48458 -40.80317,3.1656 z m -71.17456,-4.55254 c -9.86969,-2.82594 -17.84558,-10.05547 -20.86281,-18.91055 -3.71742,-10.91004 -1.74728,-13.71322 9.61708,-13.68349 7.2333,0.0189 21.75171,3.32606 27.02454,6.15589 0.38273,0.20541 -0.36979,2.49946 -1.67227,5.0979 -1.30249,2.59843 -2.67692,5.96193 -3.0543,7.47443 -0.85635,3.4322 -4.12265,3.64578 -11.46932,0.75 -2.79072,-1.1 -5.35213,-2 -5.69201,-2 -1.80911,0 3.94976,4.60152 8.86989,7.08733 5.51446,2.78608 8.82212,6.02365 8.82212,8.63517 0,1.73107 -4.18351,1.51195 -11.58292,-0.60668 z"
              id="path29"
              sodipodi:nodetypes="sssscssssssssssssssssssscsssssssscssssssssssssssssssssss" /></g></g>
        </svg>
    `,

    chatHeader: () => `
      <div id="chat-header" class="flex justify-between items-center p-5 bg-gradient-to-r from-gray-800 to-gray-900 text-white rounded-t-2xl border-b border-gray-700/50">
        <h3 class="m-0 text-lg font-semibold tracking-wide">${CONFIG.bot.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, char => char.toUpperCase())}</h3>
        <div class="flex items-center space-x-3">
          <button id="clear-chat" class="bg-transparent border-none text-white/80 hover:text-white cursor-pointer p-1 rounded-lg hover:bg-white/10 transition-all duration-200" title="Clear chat and refresh conversation">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="h-5 w-5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
            </svg>
          </button>
          <button id="expand-popup" class="bg-transparent border-none text-white/80 hover:text-white cursor-pointer p-1 rounded-lg hover:bg-white/10 transition-all duration-200" title="Expand to full screen">
            <svg id="expand-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 8V4h4M20 8V4h-4M4 16v4h4m12-4v4h-4" />
            </svg>
          </button>
          <button id="close-popup" class="bg-transparent border-none text-white/80 hover:text-white cursor-pointer p-1 rounded-lg hover:bg-white/10 transition-all duration-200" title="Close chat">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    `,

    chatMessages: () => `
      <div id="chat-messages" class="flex-1 p-5 overflow-y-auto bg-gradient-to-b from-gray-50/50 to-white/50 backdrop-blur-sm scrollbar-thin"></div>
    `,

    chatInput: () => `
      <div id="chat-input-container" class="flex-shrink-0 p-5 border-t border-gray-200/50 bg-white/80 backdrop-blur-sm rounded-b-2xl">
        <div class="flex space-x-3 items-center">
          <input type="text" id="chat-input" class="flex-1 min-w-0 border border-gray-300 rounded-xl px-4 py-3 outline-none transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 bg-white/90 placeholder-gray-500 font-medium" placeholder="Type your message...">
          <button id="chat-submit" class="chat-btn bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl px-6 py-3 cursor-pointer font-medium shadow-lg hover:shadow-xl transition-all duration-200">Send</button>
        </div>
      </div>
    `,

    imageModal: () => `
      <div id="chat-image-modal" class="fixed inset-0 bg-black/75 backdrop-blur-md flex items-center justify-center z-50 hidden transition-all duration-300">
        <div class="relative max-w-[90vw] max-h-[90vh]">
          <img id="chat-image-modal-img" src="" class="max-h-[85vh] max-w-[85vw] rounded-xl shadow-2xl" alt="Preview" />
          <button id="chat-image-modal-close" class="absolute -top-4 -right-4 bg-white opacity-80 hover:opacity-100 rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200">
            <svg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6 text-gray-800' fill='none' viewBox='0 0 24 24' stroke='currentColor' stroke-width="2">
              <path stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/>
            </svg>
          </button>
        </div>
      </div>
    `,

    waitingMessage: () => `
      <div class="flex mb-4 justify-start message-bot">
        <div class="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600 rounded-2xl rounded-bl-md py-3 px-4 max-w-[75%] flex items-center shadow-sm">
          <span class="loader mr-3"></span>
          <span class="text-sm font-medium">Thinking...</span>
        </div>
      </div>
    `,

    userMessage: (message) => `
      <div class="flex justify-end message-user break-all mb-4">
        <div class="shadow-sm bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-2xl rounded-br-md py-2 px-3 max-w-[75%] break-all">
          <div class="text-sm font-medium break-all">${utils.escapeHtml(message)}</div>
        </div>
      </div>
    `,

    errorMessage: (error) => `
      <div class="flex mb-4 message-bot">
        <div class="bg-gradient-to-r from-red-50 to-red-100 text-red-800 rounded-2xl rounded-bl-md py-3 px-4 max-w-[75%] border border-red-200 shadow-sm">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm font-medium">${utils.escapeHtml(error)}</span>
          </div>
        </div>
      </div>
    `
  };

  // Message rendering module
  const messageRenderer = {
    renderGallery: (images) => {
      if (!images || !Array.isArray(images) || images.length === 0) return '';

      const imageCount = images.length;
      let gridClass = 'chat-gallery-many';
      
      if (imageCount === 1) gridClass = 'chat-gallery-1';
      else if (imageCount === 2) gridClass = 'chat-gallery-2';
      else if (imageCount === 3) gridClass = 'chat-gallery-3';
      else if (imageCount === 4) gridClass = 'chat-gallery-4';

      return `
        <div class='chat-gallery-container ${gridClass}'>
          ${images.map((img, idx) => 
            `<img src='${img}' class='chat-gallery-img rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200' alt='Image ${idx+1}' loading='lazy' data-img='${img}'/>`
          ).join('')}
        </div>
      `;
    },

    createToggleButton: (id, label, colorClass = 'blue') => {
      const buttonId = `show-${id}`;
      const containerId = id;
      
      return {
        html: `
          <button id="${buttonId}" class="mt-3 mr-2 bg-gradient-to-r from-${colorClass}-50 to-${colorClass}-100 text-${colorClass}-700 rounded-full px-4 py-2 text-xs hover:from-${colorClass}-100 hover:to-${colorClass}-200 transition-all duration-200 border border-${colorClass}-200 font-medium">
            ${label}
          </button>
          <div id="${containerId}" class="hidden mt-3 bg-gradient-to-r from-${colorClass}-50 to-${colorClass}-100 text-${colorClass}-800 rounded-xl px-4 py-3 text-xs border border-${colorClass}-200"></div>
        `,
        attachEvents: (content, isCode = false) => {
          setTimeout(() => {
            const btn = document.getElementById(buttonId);
            const container = document.getElementById(containerId);
            if (btn && container) {
              btn.addEventListener('click', function() {
                if (isCode) {
                  container.innerHTML = `<pre class='whitespace-pre-wrap break-all font-mono text-xs'>${utils.escapeHtml(content)}</pre>`;
                } else {
                  container.innerHTML = content;
                }
                container.classList.remove('hidden');
                btn.style.display = 'none';
              });
            }
          }, 0);
        }
      };
    },

    createJSONButton: (data) => {
      const buttonId = `download-${utils.generateId()}`;
      
      return {
        html: `
          <button id="${buttonId}" class="mt-3 bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 rounded-full px-4 py-2 text-xs hover:from-blue-100 hover:to-blue-200 transition-all duration-200 border border-blue-200 font-medium">
            Download JSON
          </button>
        `,
        attachEvents: () => {
          setTimeout(() => {
            const btn = document.getElementById(buttonId);
            if (btn) {
              btn.addEventListener('click', function() {
                const success = utils.downloadJson(data);
                messageRenderer.updateButtonState(btn, success);
              });
            }
          }, 0);
        }
      };
    },

    updateButtonState: (button, success) => {
      const originalText = button.innerHTML;
      const originalClasses = button.className;
      
      if (success) {
        button.innerHTML = `
          <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Downloaded!
        `;
        button.className = originalClasses.replace(/blue/g, 'green');
      } else {
        button.innerHTML = `
          <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Error
        `;
        button.className = originalClasses.replace(/blue/g, 'red');
      }
      
      setTimeout(() => {
        button.innerHTML = originalText;
        button.className = originalClasses;
      }, success ? 2000 : 3000);
    },

    // Add XLSX download button to each table in a message element
    addXLSXButtons: (messageElement) => {
      const tables = messageElement.querySelectorAll('table');
      tables.forEach((table, idx) => {
        // Prevent duplicate buttons
        if (table.previousSibling && table.previousSibling.classList && table.previousSibling.classList.contains('xlsx-download-btn')) return;
        const btn = document.createElement('button');
        btn.innerText = 'Download as XLSX';
        btn.className = 'xlsx-download-btn ml-2 mb-2 px-3 py-1 rounded bg-green-100 text-green-800 text-xs border border-green-300 hover:bg-green-200 transition';
        btn.style.float = 'right';
        btn.addEventListener('click', () => {
          if (!window.XLSX) {
            alert('XLSX library not loaded yet. Please try again in a moment.');
            return;
          }
          const wb = window.XLSX.utils.table_to_book(table, {sheet: "Sheet 1"});
          window.XLSX.writeFile(wb, `chat-table-${idx+1}.xlsx`);
        });
        table.parentNode.insertBefore(btn, table);
      });
    }
  };

  // Event handlers module
  const eventHandlers = {
    setupChatEvents: () => {
      const chatInput = document.getElementById('chat-input');
      const chatSubmit = document.getElementById('chat-submit');
      const chatBubble = document.getElementById('chat-bubble');
      const closePopup = document.getElementById('close-popup');
      const expandPopup = document.getElementById('expand-popup');
      const clearChat = document.getElementById('clear-chat');

      if (chatSubmit) {
        chatSubmit.addEventListener('click', () => {
          const message = chatInput.value.trim();
          if (message) {
            chatInput.value = '';
            chatManager.sendMessage(message, null, null, true);
          }
        });
      }

      if (chatInput) {
        chatInput.addEventListener('keyup', (event) => {
          if (event.key === 'Enter') {
            chatSubmit.click();
          }
        });
      }

      if (chatBubble) {
        chatBubble.addEventListener('click', chatManager.togglePopup);
      }

      if (closePopup) {
        closePopup.addEventListener('click', chatManager.togglePopup);
      }

      if (expandPopup) {
        expandPopup.addEventListener('click', chatManager.toggleFullscreen);
      }

      if (clearChat) {
        clearChat.addEventListener('click', chatManager.clearChat);
      }
    },

    setupModalEvents: () => {
      const modal = document.getElementById('chat-image-modal');
      const closeBtn = document.getElementById('chat-image-modal-close');

      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          modal.classList.add('hidden');
        });
      }

      if (modal) {
        modal.addEventListener('click', (e) => {
          if (e.target === modal) {
            modal.classList.add('hidden');
          }
        });
      }
    },

    setupGalleryEvents: (container) => {
      const images = container.querySelectorAll('.chat-gallery-img');
      images.forEach(img => {
        img.addEventListener('click', function() {
          const modal = document.getElementById('chat-image-modal');
          const modalImg = document.getElementById('chat-image-modal-img');
          if (modal && modalImg) {
            modalImg.src = this.getAttribute('data-img');
            modal.classList.remove('hidden');
          }
        });
      });
    }
  };

  // Chat management module
  const chatManager = {
    togglePopup: () => {
      const chatPopup = document.getElementById('chat-popup');
      if (!chatPopup) return;

      if (chatPopup.classList.contains('hidden')) {
        chatPopup.classList.remove('hidden');
        setTimeout(() => chatPopup.classList.add('open'), 10);
        document.getElementById('chat-input')?.focus();
        state.isOpen = true;
      } else {
        chatPopup.classList.remove('open');
        setTimeout(() => chatPopup.classList.add('hidden'), 350);
        state.isOpen = false;
      }
    },

    toggleFullscreen: () => {
      const chatPopup = document.getElementById('chat-popup');
      const expandIcon = document.getElementById('expand-icon');
      const expandPopup = document.getElementById('expand-popup');

      if (!chatPopup || !expandIcon || !expandPopup) return;

      chatPopup.classList.add('fullscreen-animating');
      chatPopup.classList.toggle('fullscreen');
      
      setTimeout(() => {
        chatPopup.classList.remove('fullscreen-animating');
      }, 400);

      state.isFullscreen = chatPopup.classList.contains('fullscreen');

      if (state.isFullscreen) {
        expandPopup.title = 'Exit full screen';
        expandIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" d="M4 8h4v-4M20 8h-4v-4M4 16h4v4M20 16h-4v4" />`;
      } else {
        expandPopup.title = 'Expand to full screen';
        expandIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" d="M4 8V4h4M20 8V4h-4M4 16v4h4M20 16v4h-4" />`;
      }
    },

    clearChat: () => {
      const chatMessages = document.getElementById('chat-messages');
      if (chatMessages) {
        chatMessages.innerHTML = '';
      }
      
      // Generate new IDs
      state.conversationId = utils.generateId();
      state.dialogId = utils.generateId();
      
      // Update global variables
      window._chatWidgetConversationId = state.conversationId;
      window._chatWidgetDialogId = state.dialogId;
    },

    addMessage: (html, className = '') => {
      const chatMessages = document.getElementById('chat-messages');
      if (!chatMessages) return null;

      const messageElement = document.createElement('div');
      messageElement.className = className;
      messageElement.innerHTML = html;

      // Enhance tables for better scrolling and prevent header word-breaks
      const tables = messageElement.querySelectorAll('table');
      tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.className = 'chat-table-wrapper';
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
      });

      // Add XLSX download buttons to tables
      messageRenderer.addXLSXButtons(messageElement);

      chatMessages.appendChild(messageElement);
      return messageElement;
    },

    sendMessage: async (message, agent = null, documentType = null, showUserMessage = true) => {
      // Track last question and agent for rerun
      state.lastQuestion = message;
      state.lastAgent = agent;
      state.lastDocumentType = documentType;

      // Add user message (optional)
      if (showUserMessage) {
        chatManager.addMessage(templates.userMessage(message), 'flex justify-end mb-4 message-user');
      }

      // Add waiting indicator
      const waitingElement = chatManager.addMessage(templates.waitingMessage());

      try {
        const response = await apiClient.sendMessage(message, agent, documentType);
        
        // Remove waiting indicator
        if (waitingElement?.parentNode) {
          waitingElement.remove();
        }

        chatManager.handleResponse(response);
      } catch (error) {
        console.error('Chat error:', error);
        
        // Remove waiting indicator
        if (waitingElement?.parentNode) {
          waitingElement.remove();
        }

        chatManager.addMessage(
          templates.errorMessage('Sorry, there was an error connecting to the bot.'),
          'flex mb-4 message-bot'
        );
      }
    },

    handleResponse: (response) => {
      let parsed = null;
      
      // Parse response
      if (typeof response === 'string') {
        try {
          parsed = JSON.parse(response);
        } catch (e) {
          // Not JSON, treat as plain string
          chatManager.addMessage(
            `<div class="bg-gradient-to-r from-gray-50 to-white text-gray-800 rounded-2xl rounded-bl-md py-4 px-5 max-w-[85%] shadow-lg border border-gray-200">
              <div class="prose prose-sm max-w-none">${utils.escapeHtml(response)}</div>
            </div>`,
            'flex mb-4 message-bot'
          );
          return;
        }
      } else if (typeof response === 'object' && response !== null) {
        parsed = response;
      }

      if (!parsed) return;

      // Handle errors
      if (parsed.error) {
        chatManager.addMessage(templates.errorMessage(parsed.error), 'flex mb-4 message-bot');
        return;
      }

      // Handle selection prompts
      if (parsed.selection) {
        chatManager.handleSelection(parsed);
        return;
      }

      // Handle regular responses
      if (parsed.answer) {
        chatManager.handleAnswer(parsed);
      }
    },

    handleSelection: (data) => {
      const selectionId = `selection-${utils.generateId()}`;
      const { selection, answer } = data;
      const { is_multiple: isMultiple, choices } = selection;

      let promptText = 'Please make a selection:';
      if (answer?.formatted_answer) {
        if (answer.answer_type === 'markdown' && window.marked) {
          promptText = marked.parse(answer.formatted_answer);
        } else if (answer.answer_type === 'html') {
          promptText = answer.formatted_answer;
        } else {
          promptText = answer.formatted_answer;
        }
      }

      const optionsHtml = choices.map(opt =>
        `<label class='flex items-center space-x-3 my-2 cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors duration-150'>
          <input type='${isMultiple ? 'checkbox' : 'radio'}' name='selection-choice-${selectionId}' value='${opt}' class='w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'>
          <span class="text-sm font-medium text-gray-700">${utils.escapeHtml(opt)}</span>
        </label>`
      ).join('');

      const submitLabel = isMultiple ? 'Submit Selection' : 'Submit';
      
      const selectionHtml = `
        <div class="bg-gradient-to-r from-gray-50 to-white text-gray-800 rounded-2xl rounded-bl-md py-4 px-5 max-w-[85%] shadow-lg border border-gray-200">
          <div class='mb-4 text-sm'>${promptText}</div>
          <form id='${selectionId}' class='space-y-1'>
            ${optionsHtml}
            <button type='submit' class='mt-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200'>${submitLabel}</button>
          </form>
        </div>
      `;

      const selectionElement = chatManager.addMessage(selectionHtml, 'flex mb-4 message-bot');

      // Setup form handler
      setTimeout(() => {
        const form = document.getElementById(selectionId);
        if (form) {
          form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            let selected = [];
            if (isMultiple) {
              selected = Array.from(form.querySelectorAll('input[type="checkbox"]:checked')).map(i => i.value);
            } else {
              const radio = form.querySelector('input[type="radio"]:checked');
              if (radio) selected = [radio.value];
            }

            if (selected.length > 0) {
              // Update dialogId after submitting a selection
              state.dialogId = utils.generateId();
              window._chatWidgetDialogId = state.dialogId;

              chatManager.sendMessage(data.question, 'rag', selected, false);  // always RAG??
              // Show what the user chose
              selectionElement.innerHTML = `
                <div class='bg-gradient-to-r from-gray-50 to-white text-gray-800 rounded-2xl rounded-bl-md py-4 px-5 max-w-[85%] shadow-lg border border-gray-200'>
                  <div class='text-sm'>${promptText}</div>
                  <ul class='list-disc list-inside text-sm text-blue-700'>
                    ${selected.map(opt => `<li>${utils.escapeHtml(opt)}</li>`).join('')}
                  </ul>
                </div>
              `;
            } else {
              alert('Please make a selection before submitting.');
            }
          });
        }
      }, 0);
    },

    handleAnswer: (data) => {
      const { answer } = data;
      let html = '';

      // Format answer content
      if (answer.answer_type === 'markdown' && window.marked) {
        html = marked.parse(answer.formatted_answer);
      } else if (answer.answer_type === 'html') {
        html = answer.formatted_answer;
      } else if (answer.answer_type === 'json') {
        html = `<pre class='bg-gray-900 text-white rounded p-2 my-2 overflow-x-auto'>${JSON.stringify(answer.formatted_answer, null, 2)}</pre>`;
      } else {
        html = answer.formatted_answer;
      }

      // Create extra buttons
      let extraHtml = '';

      // Explanation button
      if (answer.explanation) {
        const explanationId = utils.generateId();
        const explanationButton = messageRenderer.createToggleButton(explanationId, 'Show Explanation', 'blue');
        extraHtml += explanationButton.html;
        explanationButton.attachEvents(answer.explanation);
      }

      // Query button
      if (answer.query) {
        const queryId = utils.generateId();
        const queryButton = messageRenderer.createToggleButton(queryId, 'Show Query', 'green');
        extraHtml += queryButton.html;
        queryButton.attachEvents(answer.query, true);
      }

      // Download button
      const downloadButton = messageRenderer.createJSONButton(data);
      extraHtml += downloadButton.html;
      downloadButton.attachEvents();

      // Document download button if source_file_details exists
      if (Object.keys(data.source_file_details).length !== 0) {
        // Always show the document download button if source_file_details exists
        const docBtnId = `download-doc-${utils.generateId()}`;
        extraHtml += `
          <button id="${docBtnId}" class="mt-3 bg-gradient-to-r from-green-50 to-green-100 text-green-700 rounded-full px-4 py-2 text-xs hover:from-green-100 hover:to-green-200 transition-all duration-200 border border-green-200 font-medium">
            <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Open Document
          </button>
        `;
        setTimeout(() => {
          const btn = document.getElementById(docBtnId);
          if (btn) {
            btn.addEventListener('click', async function() {
              btn.disabled = true;
              btn.innerHTML = 'Downloading...';
              try {
                const res = await fetch(CONFIG.apiUrl + 'document', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(data.source_file_details)
                });

                // Check for HTTP errors
                if (!res.ok) {
                  let errorMsg = 'Failed to download';
                  try {
                    const errorText = await res.text();
                    errorMsg += ': ' + errorText;
                  } catch {}
                  throw new Error(errorMsg);
                }

                // Check content type
                const contentType = res.headers.get('Content-Type') || '';
                if (!contentType.includes('pdf') && !contentType.includes('octet-stream')) {
                  const errorText = await res.text();
                  throw new Error('Server did not return a file: ' + errorText);
                }

                const blob = await res.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                let filename = 'document.pdf';
                const disposition = res.headers.get('Content-Disposition');
                if (disposition && disposition.indexOf('filename=') !== -1) {
                  filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
                }
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                btn.innerHTML = 'Downloaded!';
                btn.className = btn.className.replace(/green/g, 'blue');
              } catch (e) {
                btn.innerHTML = 'Error';
                btn.className = btn.className.replace(/green/g, 'red');
                alert(e.message || 'Download failed');
              }
              setTimeout(() => {
                btn.innerHTML = 'Download Document';
                btn.className = btn.className.replace(/red|blue/g, 'green');
                btn.disabled = false;
              }, 2000);
            });
          }
        }, 0);
      }

      // Rerun with different agent dropdown (only for last response)
      const rerunBtnId = `rerun-agent-${utils.generateId()}`;
      const agentSelectId = `agent-select-${utils.generateId()}`;
      const agentOptions = [
        { label: 'Text to SQL', value: 'text_to_sql' },
        { label: 'RAG', value: 'rag' },
        { label: 'Document RAG', value: 'rag_document' }
      ];
      extraHtml += `
        <div class="mt-3 flex items-center space-x-2 chat-rerun-controls" style="display:none">
          <button id="${rerunBtnId}" class="bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 rounded-full px-4 py-2 text-xs hover:from-blue-100 hover:to-blue-200 transition-all duration-200 border border-blue-200 font-medium">
            Rerun with agent :
          </button>
          <select id="${agentSelectId}" class="rounded border border-blue-200 px-2 py-1 text-xs focus:ring-blue-500 focus:border-blue-500">
            ${agentOptions.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('')}
          </select>
        </div>
      `;

      // Image Gallery
      const galleryHtml = messageRenderer.renderGallery(data.images);

      const responseHtml = `
        <div class="bg-gradient-to-r from-gray-50 to-white text-gray-800 rounded-2xl rounded-bl-md py-4 px-5 max-w-[85%] shadow-lg border border-gray-200" style="box-sizing:border-box;word-break:break-word;overflow-wrap:anywhere;max-width:85vw;">
          <div class="prose prose-sm max-w-none" style="word-break:break-word;overflow-wrap:anywhere;">${html}</div>
          ${extraHtml}
          <div class="chat-gallery-wrapper" style="margin-top:1rem;display:grid;grid-template-columns:repeat(auto-fit,minmax(130px,1fr));gap:14px;max-width:100%;box-sizing:border-box;">${galleryHtml}</div>
        </div>
      `;

      // Remove rerun controls from previous responses
      document.querySelectorAll('.chat-rerun-controls').forEach(el => {
        el.style.display = 'none';
      });

      const responseElement = chatManager.addMessage(responseHtml, 'flex mb-4 message-bot');

      // Show rerun controls only for the last response
      setTimeout(() => {
        const rerunControls = responseElement.querySelector('.chat-rerun-controls');
        if (rerunControls) rerunControls.style.display = 'flex';
      }, 0);

      // Setup gallery events
      if (data.images?.length > 0) {
        setTimeout(() => {

          if (eventHandlers.setupGalleryEvents) {
            eventHandlers.setupGalleryEvents(responseElement);
          } else {
            // Fallback: open image in new tab on click
            const cards = responseElement.querySelectorAll('.chat-gallery-image-card');
            cards.forEach(card => {
              card.addEventListener('click', function() {
                const img = card.querySelector('img');
                if (img && img.src) {
                  window.open(img.src, '_blank');
                }
              });
            });
          }
        }, 0);
      }

      // Setup rerun button event
      setTimeout(() => {
        const rerunBtn = document.getElementById(rerunBtnId);
        const agentSelect = document.getElementById(agentSelectId);
        if (rerunBtn && agentSelect) {
          rerunBtn.addEventListener('click', function() {
            const agent = agentSelect.value;
            if (agent) {
              chatManager.sendMessage(state.lastQuestion, agent, state.lastDocumentType, true);
            }
          });
        }
      }, 0);
    }
  };

  // API client module
  const apiClient = {
    sendMessage: async (message, agent = null, documentType = null) => {
      const body = {
        user_id: '<EMAIL>',
        conversation_id: state.conversationId,
        dialog_id: state.dialogId,
        dialog: message,
        overrides: {
          show_explanation: true,
          show_sql: true,
          top: 10,
          suggest_followup_questions: false,
          answer_type: CONFIG.answerType
        },
        preview: true,
        selected_bot: CONFIG.bot,
      };

      if (agent) body.agent_to_call = agent;
      if (documentType) body.document_types = documentType;

      const response = await fetch(CONFIG.apiUrl + 'chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      return await response.text();
    }
  };

  // Main initialization function
  function init() {
    // Load external dependencies
    if (!window.marked) {
      const markedScript = document.createElement('script');
      markedScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js';
      document.head.appendChild(markedScript);
    }

    // Inject styles
    cssInjector.injectExternalStyles();
    cssInjector.injectChatWidgetStyles();

    // Create widget container
    const chatWidgetContainer = document.createElement('div');
    chatWidgetContainer.id = 'chat-widget-container';
    document.body.appendChild(chatWidgetContainer);

    // Inject HTML
    chatWidgetContainer.innerHTML = templates.chatWidget();

    // Create modal if not exists
    if (!document.getElementById('chat-image-modal')) {
      document.body.insertAdjacentHTML('beforeend', templates.imageModal());
    }

    // Setup event handlers
    eventHandlers.setupChatEvents();
    eventHandlers.setupModalEvents();

    // Update global state
    window._chatWidgetConversationId = state.conversationId;
    window._chatWidgetDialogId = state.dialogId;

    // Show chat bubble after page load
    window.addEventListener('load', function() {
      const chatBubble = document.getElementById('chat-bubble');
      if (chatBubble) {
        chatBubble.classList.remove('chat-bubble-hidden');
        chatBubble.classList.add('chat-bubble-visible');
      }
    });
  }
  
  init();
})();